{"version": "2.0.0", "tasks": [{"label": "build lp-client-api", "type": "shell", "command": "ng build --project=lp-client-api --watch", "isBackground": true, "problemMatcher": []}, {"label": "build mobile-components", "type": "shell", "command": "ng build --project=mobile-components --watch", "isBackground": true, "problemMatcher": []}, {"label": "serve lp-client", "type": "shell", "command": "ng run lp-client:serve:${input:client}${input:env} --host=localhost --port=8100", "isBackground": true, "problemMatcher": []}, {"label": "Run All", "dependsOn": ["build lp-client-api", "build mobile-components", "serve lp-client"]}], "inputs": [{"id": "client", "type": "promptString", "description": "Enter client name"}, {"id": "env", "type": "promptString", "description": "Enter environment"}]}