{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"lp-client": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/lp-client", "sourceRoot": "projects/lp-client/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/lp-client", "index": "projects/lp-client/src/index.html", "main": "projects/lp-client/src/main.ts", "polyfills": "projects/lp-client/src/polyfills.ts", "tsConfig": "projects/lp-client/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/lp-client/src/favicon.ico", "projects/lp-client/src/assets", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-client/src/theme/theme.scss", {"input": "node_modules/@ionic/angular/css/core.css"}, {"input": "node_modules/@ionic/angular/css/normalize.css"}, {"input": "node_modules/@ionic/angular/css/structure.css"}, {"input": "node_modules/@ionic/angular/css/typography.css"}, {"input": "node_modules/@ionic/angular/css/display.css"}, {"input": "node_modules/@ionic/angular/css/padding.css"}, {"input": "node_modules/@ionic/angular/css/float-elements.css"}, {"input": "node_modules/@ionic/angular/css/text-alignment.css"}, {"input": "node_modules/@ionic/angular/css/text-transformation.css"}, {"input": "node_modules/@ionic/angular/css/flex-utils.css"}, {"input": "node_modules/flag-icons/sass/flag-icons.scss"}], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "10kb", "maximumError": "20kb"}], "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "web-dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/environment.dev.web.ts"}]}, "web": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/environment.web.ts"}], "stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/default"]}}, "ffbp": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/ffbp"]}}, "rmic": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rmic"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.rmic.dev.ts"}]}, "rmicdev": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rmic"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.rmic.dev.ts"}]}, "ffz1": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/ffz1"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.ffz1.dev.ts"}]}, "ffz1dev": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/ffz1"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/dev/environment.ffz1.dev.ts"}]}, "ffz1qa": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/ffz1"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/qa/environment.ffz1.qa.ts"}]}, "rmicqa": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rmic"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/qa/environment.rmic.qa.ts"}]}, "rmicprod": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rmic"]}, "fileReplacements": [{"replace": "projects/lp-client/src/environments/environment.ts", "with": "projects/lp-client/src/environments/prod/environment.rmic.prod.ts"}]}, "rzia": {"stylePreprocessorOptions": {"includePaths": ["projects/lp-client/src/theme", "projects/lp-client/src/theme/rzia"]}}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "lp-client:build:production"}, "web-dev": {"browserTarget": "lp-client:build:web-dev"}, "web": {"browserTarget": "lp-client:build:web"}, "ffbp": {"browserTarget": "lp-client:build:web-dev,ffbp"}, "rmic": {"browserTarget": "lp-client:build:web-dev,rmic"}, "ffz1": {"browserTarget": "lp-client:build:web-dev,ffz1"}, "ffz1dev": {"browserTarget": "lp-client:build:web-dev,ffz1"}, "ffz1qa": {"browserTarget": "lp-client:build:web-dev,ffz1qa"}, "rmicdev": {"browserTarget": "lp-client:build:web-dev,rmic"}, "rmicqa": {"browserTarget": "lp-client:build:web-dev,rmicqa"}, "rmicprod": {"browserTarget": "lp-client:build:production,rmicprod"}, "rzia": {"browserTarget": "lp-client:build:web-dev,rzia"}, "development": {"browserTarget": "lp-client:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "lp-client:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lp-client/src/test.ts", "polyfills": "projects/lp-client/src/polyfills.ts", "tsConfig": "projects/lp-client/tsconfig.spec.json", "karmaConfig": "projects/lp-client/karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["projects/lp-client/src/favicon.ico", "projects/lp-client/src/assets", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-client/src/styles.scss"], "scripts": []}}, "ionic-cordova-serve": {"builder": "@ionic/angular-toolkit:cordova-serve", "options": {"cordovaBuildTarget": "lp-client:ionic-cordova-build", "devServerTarget": "lp-client:serve"}, "configurations": {"production": {"cordovaBuildTarget": "lp-client:ionic-cordova-build:production", "devServerTarget": "lp-client:serve:production"}}}, "ionic-cordova-build": {"builder": "@ionic/angular-toolkit:cordova-build", "options": {"browserTarget": "lp-client:build"}, "configurations": {"production": {"browserTarget": "lp-client:build:production"}}}}}, "lp-client-api": {"projectType": "library", "root": "projects/lp-client-api", "sourceRoot": "projects/lp-client-api/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/lp-client-api/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/lp-client-api/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/lp-client-api/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lp-client-api/src/test.ts", "tsConfig": "projects/lp-client-api/tsconfig.spec.json", "karmaConfig": "projects/lp-client-api/karma.conf.js"}}}}, "mobile-components": {"projectType": "library", "root": "projects/mobile-components", "sourceRoot": "projects/mobile-components/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/mobile-components/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/mobile-components/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/mobile-components/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/mobile-components/src/test.ts", "tsConfig": "projects/mobile-components/tsconfig.spec.json", "karmaConfig": "projects/mobile-components/karma.conf.js"}}}}, "lp-terminal": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/lp-terminal", "sourceRoot": "projects/lp-terminal/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/lp-terminal", "index": "projects/lp-terminal/src/index.html", "main": "projects/lp-terminal/src/main.ts", "polyfills": "projects/lp-terminal/src/polyfills.ts", "tsConfig": "projects/lp-terminal/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/lp-terminal/src/favicon.ico", "projects/lp-terminal/src/assets", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-terminal/src/theme/theme.scss", {"input": "node_modules/@ionic/angular/css/core.css"}, {"input": "node_modules/@ionic/angular/css/normalize.css"}, {"input": "node_modules/@ionic/angular/css/structure.css"}, {"input": "node_modules/@ionic/angular/css/typography.css"}, {"input": "node_modules/@ionic/angular/css/display.css"}, {"input": "node_modules/@ionic/angular/css/padding.css"}, {"input": "node_modules/@ionic/angular/css/float-elements.css"}, {"input": "node_modules/@ionic/angular/css/text-alignment.css"}, {"input": "node_modules/@ionic/angular/css/text-transformation.css"}, {"input": "node_modules/@ionic/angular/css/flex-utils.css"}, {"input": "projects/lp-terminal/src/theme/variables.css"}], "scripts": []}, "configurations": {"dev": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "projects/lp-terminal/src/environments/environment.ts", "with": "projects/lp-terminal/src/environments/environment.dev.ts"}], "outputHashing": "all"}, "qa": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "projects/lp-terminal/src/environments/environment.ts", "with": "projects/lp-terminal/src/environments/environment.qa.ts"}], "outputHashing": "all"}, "prep": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "500kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "projects/lp-terminal/src/environments/environment.ts", "with": "projects/lp-terminal/src/environments/environment.prep.ts"}], "outputHashing": "all"}, "production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}], "fileReplacements": [{"replace": "projects/lp-terminal/src/environments/environment.ts", "with": "projects/lp-terminal/src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"dev": {"browserTarget": "lp-terminal:build:dev"}, "qa": {"browserTarget": "lp-terminal:build:qa"}, "prep": {"browserTarget": "lp-terminal:build:prep"}, "production": {"browserTarget": "lp-terminal:build:production"}, "development": {"browserTarget": "lp-terminal:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "lp-terminal:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "projects/lp-terminal/src/test.ts", "polyfills": "projects/lp-terminal/src/polyfills.ts", "tsConfig": "projects/lp-terminal/tsconfig.spec.json", "karmaConfig": "projects/lp-terminal/karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["projects/lp-terminal/src/favicon.ico", "projects/lp-terminal/src/assets", {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["projects/lp-terminal/src/styles.scss"], "scripts": []}}, "ionic-cordova-serve": {"builder": "@ionic/angular-toolkit:cordova-serve", "options": {"cordovaBuildTarget": "lp-terminal:ionic-cordova-build", "devServerTarget": "lp-terminal:serve"}, "configurations": {"production": {"cordovaBuildTarget": "lp-terminal:ionic-cordova-build:production", "devServerTarget": "lp-terminal:serve:production"}}}, "ionic-cordova-build": {"builder": "@ionic/angular-toolkit:cordova-build", "options": {"browserTarget": "lp-terminal:build"}, "configurations": {"production": {"browserTarget": "lp-terminal:build:production"}}}}}, "third-party-fix": {"projectType": "library", "root": "projects/third-party-fix", "sourceRoot": "projects/third-party-fix/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/third-party-fix/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/third-party-fix/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/third-party-fix/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"tsConfig": "projects/third-party-fix/tsconfig.spec.json", "polyfills": ["zone.js", "zone.js/testing"]}}}}}, "cli": {"analytics": "d5b6024d-c32c-4c49-ba86-920959896666"}}