import { HttpParams, HttpResponse } from '@angular/common/http';
import { Injectable, Injector } from '@angular/core';
import { Observable, of, map, tap, BehaviorSubject } from 'rxjs';
import {
  BasicProfile,
  LoadTransactionRequest,
  MemberProfile,
  Statement,
  TransactionReponse,
  GiftCard,
  GiftCardRequest,
} from '../types/member';
import { ContactUs } from '../types/contact-us';
import { MemberCommObject } from '../types/system';
import { AbstractService } from './abstract.service';
import moment from 'moment';
@Injectable({
  providedIn: 'root',
})
export class MemberService extends AbstractService {
  public profileSubject: BehaviorSubject<any> = new BehaviorSubject(null);
  //private profile?: MemberProfile;
  private _memberId?: string;
  private _productId?: string;
  constructor(injector: Injector) {
    super(injector);
  }
  get profile(): any {
    return this.profileSubject;
  }
  get memberId(): string {
    return this._memberId ? this._memberId : '';
  }
  get productId(): string {
    return this._productId ? this._productId : '';
  }
  memberLoad(id: any, productId?: string): Observable<any> {
    let httpParams = new HttpParams();
    if (productId != null && productId !== '') {
      httpParams = httpParams.set('productid', productId);
    }
    id = id.trim();

    return this.httpClient
      .get<MemberProfile>({
        url: `${this.apiSecure()}/member/${id}`,
        cacheMins: 0,
        key: '',
      })
      .pipe(
        map((data: MemberProfile) => {
          this.profileSubject.next(data);
          //this.profile = data;
          this._memberId = id;
          this._productId = productId;
        })
      );
  }

  memberBalance(id: any): Observable<BasicProfile> {
    id = id.trim();
    return this.httpClient
      .get<any>({
        url: `${this.apiSecure()}/member/${id}`,
        cacheMins: 0,
        key: `MEMBER-BALANCE-${id}`,
      })
      .pipe(
        map((response) => {
          return response.profileList[0];
        })
      );
  }
  memberLoadFull(id: any): Observable<MemberProfile> {
    id = id.trim();
    return this.httpClient.get<MemberProfile>({
      url: `${this.apiSecure()}/member/${id}/full`,
      cacheMins: 0,
      key: `MEMBER-FULL-${id}`,
    });
  }
  removeSession() {
    console.log('removeSession');
    this._memberId = undefined;
    this._productId = undefined;
    this.profileSubject.next(null);
  }

  loadAccrual(
    transDetails: LoadTransactionRequest
  ): Observable<TransactionReponse> {
    return this.loadTransactions(transDetails);
  }

  loadRedemption(
    transDetails: LoadTransactionRequest,
    description?: string
  ): Observable<TransactionReponse> {
    return this.loadTransactions(transDetails, description);
  }

  loadTransaction(
    transaction: LoadTransactionRequest
  ): Observable<TransactionReponse> {
    return this.loadTransactions(transaction);
  }

  private loadTransactions(
    transactionDetails: LoadTransactionRequest,
    description?: string
  ): Observable<TransactionReponse> {
    if (
      transactionDetails.transactionDetail == null ||
      transactionDetails.transactionDetail.length == 0
    ) {
      transactionDetails.transactionDetail = [
        {
          description:
            description == null ? 'Terminal Transaction' : description,
          linenumber: 1,
          quantity: 1,
          amount: transactionDetails.amount,
        },
      ];
    }

    return this.httpClient
      .postWithResponse<TransactionReponse>({
        url: `${this.apiPublic()}/member/loadtransaction`,
        key: '',
        body: transactionDetails,
      })
      .pipe(
        map((response) => {
          const authCode: string | null = response.headers.get('authCode');
          const transactionReponse: TransactionReponse = {};
          transactionReponse.basicProfile = <BasicProfile>response.body;
          transactionReponse.authCode = authCode == null ? '' : authCode;

          return transactionReponse;
        })
      );
  }

  getProfile(membershipId: string, force?: boolean): Observable<MemberProfile> {
    if (
      force ||
      !this.profileSubject.value ||
      this.profileSubject.value.newMembershipNumber !== membershipId
    ) {
      return this.memberLoadFull(membershipId).pipe(
        tap((data: MemberProfile) => {
          this.profileSubject.next(data);
        })
      );
    }
    return of(this.profile);
  }

  getTransactionHistory(
    membershipId: string,
    beginDate: Date | undefined,
    endDate: Date | undefined,
    page: number,
    pageSize: number
  ): Observable<Statement[] | undefined> {
    let dateFilter = '';
    if (beginDate) {
      dateFilter = `beginDate=${beginDate}&endDate=${endDate}&`;
    }
    return this.httpClient
      .get<Statement[]>({
        url: `${this.apiSecure()}/member/${membershipId}/statementList?${dateFilter}offset=${page}&limit=${pageSize}`,
        key: '',
      })
      .pipe(
        map((result: any) => {
          if (result.statements) {
            const statements: Statement[] = result.statements.map(
              (item: any) => {
                return { ...item, date: item.loadDate.split('T')[0] };
              }
            );
            return statements;
          } else {
            return undefined;
          }
        })
      );
  }
 
  register(profile?: MemberProfile): Observable<any> {
    if (!profile) {
      return of(null);
    }
    if (!profile.apiId) {
      profile.apiId = this.lssConfig.apiId ? this.lssConfig.apiId : '';
    }
    console.log('profile', profile);
    return this.httpClient
      .post<any>({
        url: `${this.apiPublic()}/member/`,
        key: '',
        body: profile,
      })
      .pipe(
        map((result) => {
          return result;
        })
      );
  }

  update(membershipId: string, profile?: any): Observable<any> {
    const newprofile = JSON.parse(JSON.stringify(profile));
    delete newprofile.phone;

    return this.httpClient
      .put<any>({
        url: `${this.apiSecure()}/member/${membershipId}`,
        key: '',
        body: profile,
      })
      .pipe(
        map((result) => {
          return result;
        })
      );
  }

  search(
    memberCommObject: MemberCommObject,
    cacheMins?: number
  ): Observable<BasicProfile[]> {
    return this.httpClient
      .post<any>({
        url: `${this.apiPublic()}/member/basic`,
        key: `MEM-SEARCH-${memberCommObject.membershipNumber}`,
        body: memberCommObject,
        cacheMins,
      })
      .pipe(
        map((result) => {
          let profileList: BasicProfile[] = [];

          if (result !== undefined) {
            profileList = result['profileList'];
          }

          return profileList;
        })
      );
  }

  getNotifications(membershipId: string): Observable<Statement[] | undefined> {
    console.dir('getNotifications..');

    return this.httpClient
      .get<Statement[]>({
        url: `${this.apiSecure()}/member/${membershipId}/notifications/list`,
        key: '',
      })
      .pipe(
        map((result: any) => {
          console.log('result notification', result);
          if (result) {
            return result;
          } else {
            return undefined;
          }
        })
      );
  }

  getNotificationsCount(
    membershipId: string
  ): Observable<Statement[] | undefined> {
    console.dir('getNotifications..');

    return this.httpClient
      .get<Statement[]>({
        url: `${this.apiSecure()}/member/${membershipId}/notifications/count`,
        key: '',
      })
      .pipe(
        map((result: any) => {
          console.log('result notification', result);
          if (result) {
            return result;
          } else {
            return undefined;
          }
        })
      );
  }

  getNotification(
    membershipId: string,
    notificationId: string
  ): Observable<Statement[] | undefined> {
    console.dir('getNotifications..');

    return this.httpClient
      .get<Statement[]>({
        url: `${this.apiSecure()}/member/${membershipId}/notifications/${notificationId}`,
        key: '',
      })
      .pipe(
        map((result: any) => {
          console.log('result notification', result);
          if (result) {
            return result;
          } else {
            return undefined;
          }
        })
      );
  }

  readNotification(
    membershipId: string,
    notificationId: string
  ): Observable<Statement[] | undefined> {
    console.log('readNotifications..', membershipId, notificationId);
    const body = {
      status: 'NCLO',
      closeDate: moment().format('YYYY-MM-DD HH:mm:ss'),
    };
    console.log('body', body);
    return this.httpClient
      .put({
        url: `${this.apiSecure()}/member/${membershipId}/notifications/${notificationId}`,
        key: '',
        body: body,
      })
      .pipe(
        map((result: any) => {
          console.log('result notification', result);
          if (result) {
            return result;
          } else {
            return undefined;
          }
        })
      );
  }

  updatePin(membershipId: string, pin: string): Observable<any> {
    const body = {
      pin: pin,
    };
    return this.httpClient
      .put({
        url: `${this.apiSecure()}/member/${membershipId}/pin`,
        key: 'MEMBER-PIN-UPDATE',
        body: body,
      })
      .pipe(
        map((result: any) => {
          console.log('PIN UPDATED', result);
          if (result) {
            return result;
          } else {
            return undefined;
          }
        })
      );
  }

  contactUs(membershipId: string, form?: ContactUs): Observable<any> {
    return this.httpClient
      .post<any>({
        url: `${this.apiSecure()}/member/${membershipId}/contact-us`,
        key: '',
        body: form,
      })
      .pipe(
        map((result) => {
          console.log('res', result);
          return result;
        })
      );
  }
  deleteAccount(membershipId: string, form?: ContactUs): Observable<any> {
    const reason = form ? form.message : '';
    console.log('reason', reason);
    return this.httpClient
      .delete<any>({
        url: `${this.apiSecure()}/member/${membershipId}?reason=${reason}`,
        key: '',
        body: '',
      })
      .pipe(
        map((result) => {
          console.log('res', result);
          return result;
        })
      );
  }

  validateAccount(form?: any, reference?: string): Observable<any> {
    console.log('reference', reference);
    console.log('form', form);

    return this.httpClient
      .post<any>({
        url: `${this.apiPublic()}/member/validate/${reference}`,
        key: '',
        body: form,
      })
      .pipe(
        map((result) => {
          console.log('res', result);
          return result;
        })
      );
  }
  validateAccountReference(form?: any): Observable<any> {
    console.log('form', form);
    return this.httpClient
      .post<any>({
        url: `${this.apiPublic()}/member/validate`,
        key: '',
        body: form,
      })
      .pipe(
        map((result) => {
          console.log('res', result);
          return result;
        })
      );
  }

  getGiftCard(
    memberCommObject: MemberCommObject,
    terminalId: string,
    giftCode: string,
    cacheMins?: number
  ): Observable<GiftCard> {
    let httpParams = new HttpParams()
      .set('apiId', memberCommObject.apiId!)
      .set('uniqueId', memberCommObject.uniqueId!);

    return this.httpClient
      .get<any>({
        url: `${this.apiPublic()}/giftcard/${terminalId}?giftCode=${giftCode}`,
        key: `MEM-SEARCH-${memberCommObject.membershipNumber}`,
        params: httpParams,
        cacheMins,
      })
      .pipe(
        map((result) => {
          return result;
        })
      );
  }

  fundGiftCard(
    memberCommObject: MemberCommObject,
    terminalId: string,
    giftCard: GiftCardRequest,
    cacheMins?: number
  ): Observable<GiftCard> {
    let httpParams = new HttpParams()
      .set('apiId', memberCommObject.apiId!)
      .set('uniqueId', memberCommObject.uniqueId!);

    return this.httpClient
      .put<any>({
        url: `${this.apiPublic()}/giftcard/${terminalId}`,
        key: `MEM-SEARCH-${memberCommObject.membershipNumber}`,
        params: httpParams,
        cacheMins,
        body: giftCard,
      })
      .pipe(
        map((result) => {
          return result;
        })
      );
  }

  redeemGiftCard(
    memberCommObject: MemberCommObject,
    terminalId: string,
    giftCard: GiftCardRequest,
    cacheMins?: number
  ): Observable<GiftCard> {
    let httpParams = new HttpParams()
      .set('apiId', memberCommObject.apiId!)
      .set('uniqueId', memberCommObject.uniqueId!);

    return this.httpClient
      .post<any>({
        url: `${this.apiPublic()}/giftcard/${terminalId}`,
        key: `MEM-SEARCH-${memberCommObject.membershipNumber}`,
        params: httpParams,
        cacheMins,
        body: giftCard,
      })
      .pipe(
        map((result) => {
          return result;
        })
      );
  }
}
