import { LogPublisher } from '../log/log-publishers';
import { LogLevel } from '../log/log.service';
import { DeviceDetails, Terminal } from './system';

export class LssConfig {
  /* Application Code */
  public appCode?: string;
  /* Application Name */
  public appName?: string;
  public appVersion!: string;
  /* This is to enable the authentication service */
  public useAuth?: boolean;
  /* This default url that the system will navigate to if not secured. */
  public defaultNotAuthURL?: string;
  /* Base url of the angular application. */
  public appBaseUrl?: string;
  /* URL where the application images will be stored. */
  public imageUploadUrl?: string;
  /* The context or base path of the application */
  public baseHREF?: string;
  /* The format for currency validations. */
  public currencyFormat?: string;
  /* The productId for the current user if any */
  public productId?: number;
  /* Will the app be using ION messages or Matirial. */
  public useION: boolean = false;
  /* Authentication configuration details. */
  public authConfig?: {
    issuer: string;
    clientId: string;
    scope: string;
    logoutUrl?: string;
    requireHttps?: boolean;
    url: string;
    realm: string;
    initOptions?: {
      adapter: 'default' | 'cordova' | 'cordova-native' | 'capacitor-native';
      token?: string;
      responseType?: 'code';
      scope?: string;
      onLoad: 'login-required' | 'check-sso';
      refreshToken?: string;
      redirectUri?: string;
    };
  };
  /* Will the system load roles and identity information from an API. */
  public loadIdentity?: boolean;
  /* The base URL to the identity provider. */
  public identityBaseUrl?: string;
  /* General API base URL for normal API calls. */
  public apiBaseUrl?: string;
  public apiBaseUrlMask?: string;
  /* base url to the configuration system. */
  public configAPIUrl?: string;
  /* The Hostname Based on the url */
  public hostName?: string;
  /*Current Page Name */
  public pageName?: string;
  /* Client Use ISO */
  public useISO = false;
  /* Will the system perform an auto logout on exit */
  public autoLogout = false;
  /* Auto logout timeout. */
  public autoLogoutTimeout: number = 0;
  /* Auto logout warning */
  public autoLogoutWarning: number = 0;
  /* Google API Key for interaction with Google services */
  public googleApiKey?: string;
  /* API ID to interface to LP Camel */
  public apiId?: string;
  public apiIdKeyStart!: string;
  public apiIdKeyEnd!: string;
  public termsConditions?: string;
  public pointsTitle?: string;
  public memberPhone?: any;
  public memberCard?: string;
  public contact?: any;
  /* Default latitude value for initial map view if user location cannot be obtained */
  public defaultLat?: number;
  /* Default longitude value for initial map view if user location cannot be obtained */
  public defaultLng?: number;
  public terminal?: Terminal; /* Terminal configuration */
  public terminalPoll?: number; /* Time in milliseconds that a poll must be run to validate if the current terminal is still active */
  /*This will set any theme settings for the app */
  public theme?: any;
  public navigation?: any;
  public pages?: any;
  public socials?: any;
  /*This is the device details that we extract from device */
  public deviceDetails?: DeviceDetails;
  public logConfig?: LogConfig;
  public telephone?: any;
}

export interface LogConfig {
  level: LogLevel;
  publishers: LogPublisher[];
}
