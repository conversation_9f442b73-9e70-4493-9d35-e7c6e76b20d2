// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {

    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.9.1' // Restored to original version
        classpath 'com.google.gms:google-services:4.4.0'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.0' // Added Kotlin plugin
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

apply from: "variables.gradle"
apply from: "java-compatibility.gradle"

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// Fix for "Unknown Kotlin JVM target: 21" error
subprojects {
    project.plugins.whenPluginAdded { plugin ->
        if (plugin.class.name.startsWith('org.jetbrains.kotlin')) {
            project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                kotlinOptions {
                    jvmTarget = '17' // Changed to use Java 17
                }
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
