import { CapacitorConfig } from '@capacitor/cli';
import { KeyboardResize, KeyboardStyle } from '@capacitor/keyboard';

let config: CapacitorConfig;

const baseConfig: CapacitorConfig = {
  appId: 'cloud.loyaltyplus.app',
  appName: 'Loyalty Client',
  webDir: '../../dist/lp-client',
  bundledWebRuntime: false,
  plugins: {
    Keyboard: {
      resize: KeyboardResize.Body,
      style: KeyboardStyle.Dark,
      resizeOnFullScreen: true,
    },
    CapacitorHttp: {
      enabled: true,
    },
  },
  server: {
    // hostname: 'authdev.loyaltyplus.aero',
    // hostname: 'mica-mobile.loyaltyplus.aero',
    // hostname: 'rmicdev.loyaltyplus.aero',
    hostname: 'mica-mobile.loyaltyplus.aero',
    androidScheme: 'https',
    iosScheme: 'https',
  },
};
switch (process.env['NODE_ENV']) {
  case 'rmic':
    config = {
      ...baseConfig,
      ios: {
        scheme: 'App rmic',
      },
      android: {
        flavor: 'rmic',
      },
    };
    break;
  default:
    config = {
      ...baseConfig,
      ios: {
        scheme: 'App',
      },
      android: {
        flavor: 'demo',
      },
    };
    break;
}
export default config;
