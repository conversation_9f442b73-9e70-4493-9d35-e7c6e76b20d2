PODS:
  - Capacitor (6.1.1):
    - Capac<PERSON><PERSON>ordova
  - CapacitorApp (6.0.0):
    - Capacitor
  - CapacitorBrowser (6.0.1):
    - Capacitor
  - CapacitorCommunityHttp (1.4.1):
    - Capacitor
  - CapacitorCordova (6.1.1)
  - CapacitorDevice (6.0.0):
    - Capacitor
  - CapacitorFilesystem (6.0.0):
    - Capacitor
  - CapacitorGeolocation (6.0.0):
    - Capacitor
  - CapacitorGoogleMaps (6.0.1):
    - Capacitor
    - Google-Maps-iOS-Utils (~> 5.0)
    - GoogleMaps (~> 8.4)
  - CapacitorHaptics (6.0.0):
    - Capacitor
  - CapacitorKeyboard (6.0.1):
    - Capacitor
  - CapacitorPreferences (6.0.1):
    - Capacitor
  - CapacitorStatusBar (6.0.0):
    - Capacitor
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorBrowser (from `../../node_modules/@capacitor/browser`)"
  - "CapacitorCommunityHttp (from `../../node_modules/@capacitor-community/http`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorFilesystem (from `../../node_modules/@capacitor/filesystem`)"
  - "CapacitorGeolocation (from `../../node_modules/@capacitor/geolocation`)"
  - "CapacitorGoogleMaps (from `../../node_modules/@capacitor/google-maps`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorPreferences (from `../../node_modules/@capacitor/preferences`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"

SPEC REPOS:
  trunk:
    - Google-Maps-iOS-Utils
    - GoogleMaps

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorBrowser:
    :path: "../../node_modules/@capacitor/browser"
  CapacitorCommunityHttp:
    :path: "../../node_modules/@capacitor-community/http"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorFilesystem:
    :path: "../../node_modules/@capacitor/filesystem"
  CapacitorGeolocation:
    :path: "../../node_modules/@capacitor/geolocation"
  CapacitorGoogleMaps:
    :path: "../../node_modules/@capacitor/google-maps"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorPreferences:
    :path: "../../node_modules/@capacitor/preferences"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"

SPEC CHECKSUMS:
  Capacitor: 8941aba4364ba9d1b22188569001f2ce45cc2b00
  CapacitorApp: 9d53aec7101f7b030a950c5bdc4df8612576b279
  CapacitorBrowser: 473c7fd70ddbe541608ff09ec1be14da0078279e
  CapacitorCommunityHttp: 7be90668527ef14ee10d08135b0e00fac9cf8247
  CapacitorCordova: 8f2cc8d8d3619c566e9418fe8772064a94266106
  CapacitorDevice: f8fd88f9edd1261c55a109f32015b09bbbfdc4a0
  CapacitorFilesystem: 60e59ba274c234a979e7a3be2552feaadcee4263
  CapacitorGeolocation: 1f12bbe372b65116e851bd8e3a94cf0ef9bacebb
  CapacitorGoogleMaps: bafda318e41a1aab9f00571f6a87fb376b63c0c9
  CapacitorHaptics: 9ebc9363f0e9b8eb4295088a0b474530acf1859b
  CapacitorKeyboard: 5f32a712adf41e07a61caafb82cf29fb6d8ba123
  CapacitorPreferences: 72909b165bc7807103778ddbb86d5d8ce06abf71
  CapacitorStatusBar: 2e4369f99166125435641b1908d05f561eaba6f6
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d

PODFILE CHECKSUM: 1d5bc01d5ed21e6d12324e0757022ed004270407

COCOAPODS: 1.16.2
