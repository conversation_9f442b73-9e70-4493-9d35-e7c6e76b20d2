<ion-app id="app" class="ion-page app-background bg-app">
  <ion-header class="ion-no-border header-backgrounds" *ngIf="loggedin">
    <ion-toolbar>
      <ion-buttons slot="start" *ngIf="showMenuBack">
        <ion-button class="header-button" (click)="back()">
          <ion-icon
            style="color: white"
            slot="icon-only"
            name="arrow-back-outline"
          ></ion-icon>
        </ion-button>
      </ion-buttons>
      <ion-title class="page-titles" [routerLink]="['/']"
        >{{ pageText }}
      </ion-title>
      <ion-buttons slot="end">
        <ion-button
          id="notification-button"
          class="header-button"
          [routerLink]="'/public/notifications'"
          *ngIf="loggedin"
        >
          <ion-badge id="notifications-badge">{{ count }}</ion-badge>
          <ion-icon style="color: white" name="notifications-outline">
          </ion-icon>
        </ion-button>
        <ion-menu-button class="header-button"></ion-menu-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>

  <ion-content
    id="app-content"
    class="app-background bg-app ion-padding bg-app"
  >
    <ion-router-outlet></ion-router-outlet>
  </ion-content>

  <ion-menu class="menu" side="end" menuId="main-menu2" contentId="app-content">
    <div class="blur"></div>
    <ion-content>
      <ion-item
        class="app"
        lines="none"
        *ngIf="lssConfig.navigation.sidebarIcon"
      >
        <img [src]="lssConfig.navigation.sidebarIcon" alt="" />
      </ion-item>

      <!-- <ion-item class="lang-selector" lines="none">
        <ion-icon slot="start" name="language-outline"></ion-icon>
        <ion-select value="en" interface="popover">
            <ion-select-option value="en">English</ion-select-option>
            <ion-select-option value="fr">French</ion-select-option>
        </ion-select>
      </ion-item> -->

      <ion-list lines="none" class="menu-items">
        <div *ngIf="loggedin">
          <ion-menu-toggle *ngFor="let item of menuList; index as i">
            <ion-item
              class="menu-item"
              [ngClass]="{ active: item.link == '/' + pageTitle }"
              [routerLink]="[item.link]"
            >
              <span class="title">{{ item.text }}</span>
            </ion-item>
          </ion-menu-toggle>
        </div>

        <ion-menu-toggle>
          <ion-item class="menu-item" (click)="logout()" *ngIf="loggedin">
            <span class="title">Sign Out</span>
          </ion-item>
        </ion-menu-toggle>
        <ion-menu-toggle>
          <ion-item class="menu-item" (click)="login()" *ngIf="!loggedin">
            <span class="title">Sign In</span>
          </ion-item>
        </ion-menu-toggle>
        <ion-menu-toggle>
          <ion-item
            class="menu-item"
            [routerLink]="['/public/validate']"
            *ngIf="!loggedin"
            menu-close
          >
            <span class="title">Sign Up</span>
          </ion-item>
        </ion-menu-toggle>
        <ion-menu-toggle>
          <ion-item
            class="menu-item"
            [routerLink]="['/public/password']"
            *ngIf="!loggedin"
          >
            <span class="title">Forgot Password</span>
          </ion-item>
        </ion-menu-toggle>
      </ion-list>
    </ion-content>
  </ion-menu>
</ion-app>
