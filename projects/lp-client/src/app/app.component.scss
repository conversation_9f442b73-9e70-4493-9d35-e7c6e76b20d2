.app-background {
    --background: var(--ion-color-base)
}

.header-backgrounds {
    background: var(--ion-color-base-shade);
  box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);

}

#notification-button {         
    position: relative;
    width: 42px;
    top:1px;
    right: 1px;
    overflow: visible!important;
}


#notifications-badge {
    --background: transparent !important;
    position: absolute;
    top: -3px;
    right: -9px;
    border-radius: 100%;
}
.bg-app {
    background: var(--ion-color-base) !important;
  }
.app {
    --background: var(--ion-color-base) !important;
}
.page-titles {
    color: var(--ion-color-primary-contrast);
margin-left: 12px;
}

ion-icon {
    --color: var(--ion-color-base) !important;

}

.header-button {
    color: var(--ion-color-base-contrast);
}
.app-menu {
    .menu-bg {
        height: 180px;
        width: 350px;
        background: var(--ion-color-primary);
        box-shadow: 0px 3px 10px var(--ion-color-primary-shade);
        //box-shadow: 0px 3px 10px rgba(98, 140, 255, 0.5);
        transform: rotate(-10deg);
        border-radius: 10px 10px 10px 50px;
        margin-left: -22px;
        margin-top: -60px;
    }

    .menu-content {
        position: absolute;
        top: 30px;
        left: 15px;
        display: flex;
        align-items: center;
        margin-right: 14px;

        img {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-right: 14px;
        }

        h2 {
            font-weight: 300;
            color: #fff;
        }

        p {
            font-size: 14px;
            color: #e4e4e4;
            font-weight: 100;
            letter-spacing: 0.4px;
        }
    }

    .lang-selector {
        display: flex;
        justify-content: center;
        margin-top: 34px;

        ion-item {
            width: 70%;
        }

        ion-select {
            width: 100%;
        }
    }

    .menu-items {
        margin: 10px 0px 0px 0px;
    
        fa-icon {
            margin-right: 20px;
            color: #86979f;
        }
    
        ion-item {
            padding-left: 20px;
            margin-bottom: 10px;
        }
    
        .active {
            border-left: 5px solid;
            color: var(--ion-color-secondary);
            padding-left: 5px;
    
            fa-icon {
                color: var(--ion-color-secondary);
            }
        }
    }
}


ion-title {
    position: absolute;
    top: 0;
    left: 0;
    padding: 0 90px 1px;
    width: 100%;
    height: 100%;
    text-align: center;
  }