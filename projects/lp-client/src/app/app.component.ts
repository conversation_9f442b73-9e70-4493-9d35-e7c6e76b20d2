import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { LocationStrategy } from '@angular/common';

import { Router } from '@angular/router';
import { LoadingController, MenuController } from '@ionic/angular';
import {
  AbstractService,
  KeyCloakService,
  LogService,
  LssConfig,
  MemberProfile,
  MemberService,
} from 'lp-client-api';
import { App, URLOpenListenerEvent } from '@capacitor/app';
import { environment } from '../environments/environment';
import { Platform } from '@ionic/angular';
import { Preferences } from '@capacitor/preferences';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {
  menuList = [
    { icon: 'home', text: 'Home', link: 'public/home' },
    { icon: 'images', text: 'Photo', link: 'pages/home' },
    { icon: 'person-circle-outline', text: 'Profile', link: 'pages/profile' },
  ];
  count?: any = 0;
  loading?: HTMLIonLoadingElement;
  slowLoad = false;
  environment = environment;

  _currentMenu = { icon: '', text: '', link: '' };

  constructor(
    private menu: MenuController,
    private kc: KeyCloakService,
    protected readonly router: Router,
    private memberService: MemberService,
    private logService: LogService,
    private platform: Platform,
    private loadingCtrl: LoadingController,
    public lssConfig: LssConfig,
    private LocationStrategy: LocationStrategy
  ) {
    this.platform.ready().then(() => {
      if (this.platform.is('hybrid')) {
        App.addListener('appStateChange', ({ isActive }) => {
          console.log('App state changed. Is active?', isActive);
          if (isActive && this.kc.authSuccess) {
            if (this.kc.keycloak?.tokenParsed) {
              if (this.kc.keycloak.tokenParsed.exp) {
                if (
                  this.kc.keycloak.tokenParsed.exp <
                  new Date().getTime() / 1000
                ) {
                  this.kc.authed().then((authed: boolean) => {
                    if (!authed) {
                      console.log('Go to login screen');
                    }
                  });
                }
              }
            }
          }
        });
        App.addListener('appUrlOpen', (data) => {
          console.log('App opened with URL:', data);
        });
      }
    });
  }

  ngOnInit() {
    this.kc.authStatus.subscribe((data) => {
      console.log('this.kc.authStatus', data);
      if (data != null && data.eventName === 'init') {
        this.loadingCtrl
          .create({
            message: 'Loading Authentication',
            duration: 10000,
          })
          .then((data) => {
            console.log('Loading created');
            if (!this.slowLoad) {
              this.loading = data;
              this.loading.present();
            } else {
              this.slowLoad = false;
            }
          });
      } else {
        this.slowLoad = true;
        if (this.loading) {
          this.loading.dismiss();
        }
      }
      if (data != null) {
        if (data.eventName === 'login') {
          if (this.kc.keycloak) {
            Preferences.set({
              key: 'login',
              value: JSON.stringify({
                refreshToken: this.kc.keycloak.refreshToken,
                idToken: this.kc.keycloak.idToken,
                token: this.kc.keycloak.token,
              }),
            }).then(() => {
              console.log('Token Login');
            });
          }
          this.getNotificationCount();
          this.memberService
            .getProfile(this.kc.lpUniueReference)
            .subscribe((profile: MemberProfile) => {
              if (this.router.url === '/public/signup') {
                this.router.navigate(['/app/account']);
              }
            });
        } else if (data.eventName === 'refresh') {
          if (this.kc.keycloak) {
            Preferences.set({
              key: 'login',
              value: JSON.stringify({
                refreshToken: this.kc.keycloak.refreshToken,
                idToken: this.kc.keycloak.idToken,
                token: this.kc.keycloak.token,
              }),
            }).then(() => {
              console.log('Token Refresh');
            });
          }
        } else if (data.eventName === 'expired') {
          //this.kc.keycloak?.updateToken(60000);
        } else if (data.eventName === 'logout') {
          Preferences.remove({ key: 'login' }).then(() => {
            console.log('Token Removed');
          });
          this.memberService.removeSession();
          this.router.navigate(['/']);
        }
      }
    });

    this.generateMenu();
    const htmlEl: any = document.querySelector('html');
    const theme = this.lssConfig.theme;
    htmlEl.style.setProperty('--ion-color-primary', theme.colours.primary);
    htmlEl.style.setProperty(
      '--ion-color-primary-contrast',
      theme.colours.primaryContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-primary-shade',
      theme.colours.primaryShade
    );
    htmlEl.style.setProperty(
      '--ion-color-primary-tint',
      theme.colours.primaryTint
    );

    htmlEl.style.setProperty('--ion-color-secondary', theme.colours.secondary);
    htmlEl.style.setProperty(
      '--ion-color-secondary-contrast',
      theme.colours.secondaryContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-secondary-shade',
      theme.colours.secondaryShade
    );
    htmlEl.style.setProperty(
      '--ion-color-secondary-tint',
      theme.colours.secondaryTint
    );

    htmlEl.style.setProperty('--ion-color-tertiary', theme.colours.tertiary);
    htmlEl.style.setProperty(
      '--ion-color-tertiary-contrast',
      theme.colours.tertiaryContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-tertiary-shade',
      theme.colours.tertiaryShade
    );
    htmlEl.style.setProperty(
      '--ion-color-tertiary-tint',
      theme.colours.tertiaryTint
    );

    htmlEl.style.setProperty('--ion-color-success', theme.colours.success);
    htmlEl.style.setProperty(
      '--ion-color-success-contrast',
      theme.colours.successContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-success-shade',
      theme.colours.successShade
    );
    htmlEl.style.setProperty(
      '--ion-color-success-tint',
      theme.colours.successTint
    );

    htmlEl.style.setProperty('--ion-color-warning', theme.colours.warning);
    htmlEl.style.setProperty(
      '--ion-color-warning-contrast',
      theme.colours.warningContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-warning-shade',
      theme.colours.warningShade
    );
    htmlEl.style.setProperty(
      '--ion-color-warning-tint',
      theme.colours.warningTint
    );

    htmlEl.style.setProperty('--ion-color-danger', theme.colours.danger);
    htmlEl.style.setProperty(
      '--ion-color-danger-contrast',
      theme.colours.dangerContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-danger-shade',
      theme.colours.dangerShade
    );
    htmlEl.style.setProperty(
      '--ion-color-danger-tint',
      theme.colours.dangerTint
    );

    htmlEl.style.setProperty('--ion-color-medium', theme.colours.medium);
    htmlEl.style.setProperty(
      '--ion-color-medium-contrast',
      theme.colours.mediumContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-medium-shade',
      theme.colours.mediumShade
    );
    htmlEl.style.setProperty(
      '--ion-color-medium-tint',
      theme.colours.mediumTint
    );

    htmlEl.style.setProperty('--ion-color-base', theme.colours.base);
    htmlEl.style.setProperty(
      '--ion-color-base-contrast',
      theme.colours.baseContrast
    );
    htmlEl.style.setProperty('--ion-color-base-shade', theme.colours.baseShade);
    htmlEl.style.setProperty('--ion-color-base-tint', theme.colours.baseTint);

    htmlEl.style.setProperty('--ion-color-light', theme.colours.light);
    htmlEl.style.setProperty(
      '--ion-color-light-contrast',
      theme.colours.lightContrast
    );
    htmlEl.style.setProperty(
      '--ion-color-light-shade',
      theme.colours.lightShade
    );
    htmlEl.style.setProperty('--ion-color-light-tint', theme.colours.lightTint);
  }

  ngOnDestroy(): void {
    this.kc.authStatus.unsubscribe();
  }

  generateMenu(): void {
    // todo: generate this from settings
    this.menuList = this.lssConfig.navigation.routes.filter(
      (rt: any) => rt.sidebar
    );
  }

  openMenu() {
    this.menu.open('end');
  }

  get loggedin(): boolean | undefined {
    return this.kc.authSuccess;
  }

  get pageTitle(): string {
    let url = this.router.url;
    if (url === '/') {
      url = 'pages/home';
    } else {
      url = url.substring(1);
    }
    if (this._currentMenu == null || url !== this._currentMenu.link) {
      this._currentMenu = this.menuList.filter(
        (filter) => url === filter.link
      )[0];
    }
    return url; // todo: switch back to returning from menu page title
  }

  get pageText(): string {
    let url = this.router.url;
    let text: any = '';
    if (url === '/public/storedetail') return 'Store Detail';
    if (url == '/secure/security') return 'Security';
    if (url == '/secure/profile') return 'Profile';

    text = this.menuList.filter((filter) => url === filter.link)[0];
    return text ? text.text : 'Home'; // todo: switch back to returning from menu page title
  }

  close(menuItem: any) {
    this.menu.close();
    this._currentMenu = menuItem;
  }
  back(): void {
    if (this.LocationStrategy.historyGo) this.LocationStrategy.historyGo(-1);
  }
  login() {
    if (!this.kc.authSuccess) {
      this.kc.keycloak?.login().then((data) => {
        console.log(data);
      });
    }
  }

  logout() {
    if (this.kc.authSuccess) {
      this.kc.keycloak?.logout();
    }
  }

  getNotificationCount() {
    this.memberService
      .getNotificationsCount(this.kc.lpUniueReference)
      .subscribe({
        error: (error) => {
          console.log(error.message);
        },
        next: (body: any) => {
          console.log('body', body.count);
          if (body !== undefined) {
            this.count = body.count;
          }
        },
      });
  }

  get showMenuBack(): boolean | undefined {
    return this.router.url.includes('/app/home') ? false : true;
  }
}
