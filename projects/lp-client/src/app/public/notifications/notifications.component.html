<ion-content class="app-background">
  <div class="state-empty" *ngIf="notifications.length == 0">
    <h3>No Notifications</h3>
    <img src="./assets/images/empty-notification.png" />
    <p>You have no new notifications</p>
  </div>
  <div *ngIf="notifications.length > 0" class="state-full">
    <ion-accordion-group>
      <ion-accordion *ngFor="let noti of notifications" [value]="noti.noteSeq">
        <ion-item slot="header" color="light">
          <ion-label>{{ noti.shortDescription }}</ion-label>
        </ion-item>
        <div class="ion-padding" slot="content">
          <ion-badge style="float: right" (click)="notiRead(noti.noteSeq)">
            <ion-icon name="checkmark-outline"></ion-icon>
          </ion-badge>
          <ion-text>
            {{ noti.message.split("\\")[1] }}
          </ion-text>
        </div>
      </ion-accordion>
    </ion-accordion-group>
  </div>
</ion-content>
