<ion-header [translucent]="true">
  <ion-toolbar class="header-backgrounds">
    <ion-buttons slot="start">
      <ion-button class="" [routerLink]="['/']">
        <ion-icon slot="icon-only" name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title> Forgot Password </ion-title>
    <ion-buttons slot="end"> </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content class="app-background">
  <ion-grid>
    <ion-row class="center">
      <ion-col>
        <div>
          <!-- <ion-button expand="block" class="points">{{ profile?.currentBalance! }}</ion-button> -->
        </div>
      </ion-col>
      <ion-col>
        <div class="logo-col">
          <img
            class="logo"
            [src]="environment.lssConfig.pages.landing.loggedinIcon"
            alt=""
          />
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>

  <!-- <ion-text class="page-title">
    <p>Reset Password</p>
  </ion-text> -->
  <div class="ion-padding">
    <form class="form" [formGroup]="_form" (ngSubmit)="resetPassword()">
      <ion-item lines="none" fill="outline">
        <ion-label position="floating">Account Number</ion-label>
        <ion-icon slot="start" name="person"></ion-icon>
        <ion-input type="text" formControlName="accountNumber"></ion-input>
      </ion-item>
      <ion-item lines="none" *ngIf="isFormComponentInvalid('accountNumber')">
        <div
          *ngFor="let error of getComponentErrors('accountNumber')"
          class="validator-error"
        >
          <ion-note slot="error">{{ error }}</ion-note>
        </div>
      </ion-item>
      <div class="form-spacer"></div>
      <ion-button
        expand="block"
        class="save"
        type="submit"
        [disabled]="!isFormValid()"
        >Reset</ion-button
      >
    </form>
  </div>
</ion-content>
<ion-content class="ion-padding" *ngIf="isformState(_formStateType.update)">
  <form class="form" [formGroup]="_form" (ngSubmit)="resetPassword()">
    <ion-item lines="none">
      <ion-label position="floating">Password</ion-label>
      <ion-icon slot="start" name="lock-closed"></ion-icon>
      <ion-input type="password" formControlName="password"></ion-input>
    </ion-item>
    <ion-item lines="none" *ngIf="isFormComponentInvalid('password')">
      <div
        *ngFor="let error of getComponentErrors('password')"
        class="validator-error"
      >
        {{ error }}
      </div>
    </ion-item>
    <ion-item lines="none">
      <ion-label position="floating">Confirm Password</ion-label>
      <ion-icon slot="start" name="lock-closed"></ion-icon>
      <ion-input type="password" formControlName="passwordConfirm"></ion-input>
    </ion-item>
    <ion-item lines="none" *ngIf="isFormComponentInvalid('passwordConfirm')">
      <div
        *ngFor="let error of getComponentErrors('passwordConfirm')"
        class="validator-error"
      >
        {{ error }}
      </div>
    </ion-item>
    <div class="form-spacer"></div>
    <ion-button
      expand="block"
      class="save"
      type="submit"
      [disabled]="!isFormValid()"
      >Reset</ion-button
    >
  </form>
</ion-content>
