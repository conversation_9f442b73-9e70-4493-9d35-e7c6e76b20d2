import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { GoogleMapsModule } from '@angular/google-maps';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { IonicModule } from '@ionic/angular';
import { ComponentsModule } from 'mobile-components';
import { IonIntlTelInputModule } from 'third-party-fix';
import { HomeComponent } from './home/<USER>';
import { LandingComponent } from './landing/landing.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { PasswordComponent } from './password/password.component';
import { PublicRoutingModule } from './public-routing.module';
import { SignupComponent } from './signup/signup.component';
import { StoreDetailComponent } from './store-detail/store-detail.component';
import { StoresComponent } from './stores/stores.component';
import { ValidateComponent } from './validate/validate.component';
import { OtpComponent } from './otp/otp.component';

@NgModule({
  declarations: [
    HomeComponent,
    LandingComponent,
    SignupComponent,
    PasswordComponent,
    NotificationsComponent,
    StoresComponent,
    StoreDetailComponent,
    ValidateComponent,
    OtpComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    IonIntlTelInputModule,
    ReactiveFormsModule,
    FontAwesomeModule,
    GoogleMapsModule,
    PublicRoutingModule,
    ComponentsModule,

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PublicModule {}
