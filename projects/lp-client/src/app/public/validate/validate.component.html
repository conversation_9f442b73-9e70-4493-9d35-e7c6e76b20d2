<ion-header [translucent]="true">
  <ion-toolbar class="header-backgrounds">
    <ion-buttons slot="start">
      <ion-button class="" [routerLink]="['/']">
        <ion-icon slot="icon-only" name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title> OTP </ion-title>
    <ion-buttons slot="end"> </ion-buttons>
  </ion-toolbar>
</ion-header>
<ion-content class="app-background">
  <!-- <lib-head-logo
  [names]="profile.givenNames + ' ' + profile.surname"
  [membership]="profile.newMembershipNumber"
  type="welcome"
  [balance]="profile.currentBalance"
  [src]="lssConfig.pages.landing.loggedinIcon"
/> -->
  <lib-head-logo
  type="welcome"
  names="OTP Request"
      
      [src]="lssConfig.pages.landing.loggedinIcon"
    />

  <ion-card class="">
    <form [formGroup]="_form">
      <ion-item>
        <ion-input
          labelPlacement="floating"
          label="Card Number"
          type="text"
          formControlName="Cardnumber"
          placeholder="Card Number"
        ></ion-input>
      </ion-item>
      <!-- <ion-item>
        <ion-input
          labelPlacement="floating"
          label="Email"
          type="text"
          formControlName="email"
          placeholder="Email"
        ></ion-input>
      </ion-item> -->
      <ion-item lines="inset" class="ion-intl-tel item-has-value">
        <ion-label position="floating">* Mobile Number:</ion-label>
        <ion-intl-tel-input
          id="phone-number"
          labelPlacement="floating"
          label="* Mobile Number:"
          name="phone-number"
          formControlName="phone"
          [enableAutoCountrySelect]="true"
          [selectFirstCountry]="
            lssConfig.telephone.selectFirstCountry
          "
          [preferredCountries]="
            lssConfig.telephone.preferredCountries
          "
        ></ion-intl-tel-input>
      </ion-item>
      <ion-item *ngIf="isFormComponentInvalid('phone')">
        <div
          *ngFor="let error of getComponentErrors('phone')"
          class="validator-error"
        >
          <!-- {{ error }} -->
          5 Digits needed when you spend points.
        </div>
      </ion-item>
      <!-- {{form.phone.valid}}-
      {{form.phone.touched}} -->
      <ion-item *ngIf="!isMyFormValid() && isMyPhoneValid()">
        <div
          class="validator-error"
        >
         Please fill in a valid Mobile Number
        </div>
        <ion-item *ngIf="showError">
          <p class="validator-error" >
            The provided OTP does not match the one associated with the details given.
          </p>
        </ion-item>
      </ion-item>
      <!-- {{isMyFormValid()}} -->
        <ion-button expand="block" class="submit" (click)="submit()" [disabled]="!isMyFormValid()"
        >Request OTP</ion-button
      >
      <ion-item *ngIf="showLoginButton">

        <ion-button expand="block" class="submit" (click)="login()"
        >Login</ion-button
      >
      <ion-button expand="block" class="submit" (click)="lostPassword()"
      >Forgot Password</ion-button
    >
      </ion-item>

    
      
    </form>
  </ion-card>

  <ion-modal [isOpen]="isModalOpen">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-title>OTP</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setOpen(false)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content>
          <!--   <ion-item lines="inset" class="ion-intl-tel item-has-value">
      <ion-label position="floating">*EMAIL OTP:</ion-label> -->
          <!-- <ion-input
            id="phone-number"
            labelPlacement="floating"
            label="* Email OTP:"
            name="smsotp"
            [(ngModel)]="emailotp"
          ></ion-input> 
        </ion-item>-->
        <ion-item lines="inset" class="ion-intl-tel item-has-value mt-4">
          <!-- <ion-label position="floating">*SMS OTP:</ion-label> -->
          <ion-input
            id="phone-number"
            labelPlacement="floating"
            label="* SMS OTP:"
            name="smsotp"
            [(ngModel)]="smsotp"
          ></ion-input>
        </ion-item>
        <!-- <ion-item lines="inset" class="ion-intl-tel item-has-value"> -->
          <!-- <ion-label position="floating">*EMAIL OTP:</ion-label> -->
          <!-- <ion-input
            id="phone-number"
            labelPlacement="floating"
            label="* Email OTP:"
            name="smsotp"
            [(ngModel)]="emailotp"
          ></ion-input> -->
        <!-- </ion-item> -->
        <ion-button expand="block" class="submit" (click)="submitOtp()"
          >Confirm</ion-button
        >
      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>
