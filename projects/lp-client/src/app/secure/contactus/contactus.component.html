<ion-content class="app-background">

  <lib-head-logo type="phone" [phone]="lssConfig.contact.callCenter" [src]="lssConfig.pages.landing.loggedinIcon" />
    <ion-card-content class="formbackground">
    <form [formGroup]="_form">
      <ion-item>
          <ion-select label="Category"
          labelPlacement="floating"
          formControlName="category"
           placeholder="Please select..">
           <ion-select-option *ngFor="let codeItem of getCodeList(lssConfig.pages.contact.categoryCode) | async" [value]="codeItem.codeId">{{codeItem.description}}</ion-select-option>
        </ion-select>
      </ion-item>
      <ion-item>
        <ion-input
          labelPlacement="floating"
          label="Name"
          type="text"
          formControlName="givenNames"
        ></ion-input>
      </ion-item>
      <ion-item>
        <ion-input
          labelPlacement="floating"
          label="Surname"
          type="text"
          formControlName="surname"
        ></ion-input>
      </ion-item>
      <ion-item >
        <ion-input
          labelPlacement="floating"
          label="Email"
          type="email"
          formControlName="email"
        ></ion-input>
      </ion-item>
      <ion-item lines="inset" class="ion-intl-tel item-has-value">
        <ion-label position="floating">* Mobile Number:</ion-label>
        <ion-intl-tel-input id="phone-number" labelPlacement="floating" label="* Mobile Number:" name="phone-number" formControlName="phone" [enableAutoCountrySelect]="true" [selectFirstCountry]="lssConfig.telephone.selectFirstCountry" [preferredCountries]="lssConfig.telephone.preferredCountries"></ion-intl-tel-input>
    </ion-item>
      <!-- <ion-item lines="none">
        <ion-input
          labelPlacement="floating"
          label="Number"
          type="number"
          formControlName="phone"
        ></ion-input>
      </ion-item> -->
      <ion-item>
        <ion-textarea  labelPlacement="floating"
        label="Message"
        type="text"
        formControlName="message" placeholder="Message"></ion-textarea>
      </ion-item>
      <ion-button expand="block" class="submit"  (click)="submit()">Log Message</ion-button>
    </form>
  </ion-card-content>
</ion-content>
