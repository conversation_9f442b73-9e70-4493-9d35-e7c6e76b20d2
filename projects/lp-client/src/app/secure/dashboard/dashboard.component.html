<ion-content class="app-background">
  <!-- Modern Header -->
  <lib-head-logo
    [names]="profile?.givenNames + ' ' + profile?.surname"
    [membership]="profile?.newMembershipNumber"
    type="welcome"
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />
  
  <!-- Glass Balance Cards -->
  <div class="glass-balance-cards">
    <div class="glass-card primary">
      <div class="card-content">
        <p class="card-label">
          <ion-icon name="cash-outline"></ion-icon>
          Rand Value
        </p>
        <p class="card-value">R {{ profile?.availRands?.toFixed(2) || '0.00' }}</p>
      </div>
    </div>
    
    <div class="glass-card points">
      <div class="card-content">
        <p class="card-label">
          <ion-icon name="star-outline"></ion-icon>
          Points
        </p>
        <p class="card-value">{{ profile?.currentBalance || 0 }}</p>
      </div>
    </div>
  </div>

  <!-- Modern Action Buttons -->
  <div class="modern-actions">
    <h2 class="actions-title">Quick Actions</h2>
    <div class="modern-grid">
      <!-- Profile -->
      <a [routerLink]="['/secure/profile']" class="modern-action-card profile">
        <div class="action-decoration"></div>
        <div class="action-icon">
          <ion-icon name="person-outline"></ion-icon>
        </div>
        <p class="action-label">Profile</p>
      </a>
      
      <!-- Security -->
      <a [routerLink]="['/secure/security']" class="modern-action-card security">
        <div class="action-decoration"></div>
        <div class="action-icon">
          <ion-icon name="shield-checkmark-outline"></ion-icon>
        </div>
        <p class="action-label">Security</p>
      </a>
      
      <!-- Pools -->
      <a [routerLink]="['/secure/pools']" class="modern-action-card pools">
        <div class="action-decoration"></div>
        <div class="action-icon">
          <ion-icon name="layers-outline"></ion-icon>
        </div>
        <p class="action-label">Pools</p>
      </a>
      
      <!-- Points -->
      <a [routerLink]="['/secure/points']" class="modern-action-card points-btn">
        <div class="action-decoration"></div>
        <div class="action-icon">
          <ion-icon name="trophy-outline"></ion-icon>
        </div>
        <p class="action-label">Points</p>
      </a>
    </div>
  </div>

  <!-- Points Summary Section -->
  <div class="points-summary">
    <h2 class="section-title">Points Summary</h2>
    <ion-card class="summary-card">
      <ion-item lines="none" class="summary-item">
        <ion-label>Available Balance</ion-label>
        <ion-text slot="end" class="summary-value">{{ profile?.availUnits || 0 }}</ion-text>
      </ion-item>
      
      <ion-item lines="none" [routerLink]="['/secure/points']" class="summary-item clickable">
        <ion-label>{{ lssConfig.pointsTitle || 'Points' }}</ion-label>
        <ion-text slot="end" class="summary-value primary">{{ profile?.currentBalance || 0 }}</ion-text>
        <ion-icon name="chevron-forward-outline" slot="end" class="nav-arrow"></ion-icon>
      </ion-item>
      
      <ion-item lines="none" [routerLink]="['/secure/points']" class="summary-item clickable">
        <ion-label>Earned</ion-label>
        <ion-text slot="end" class="summary-value success">{{ (profile?.baseMiles || 0) + (profile?.bonusMiles || 0) }}</ion-text>
        <ion-icon name="chevron-forward-outline" slot="end" class="nav-arrow"></ion-icon>
      </ion-item>
      
      <ion-item lines="none" class="summary-item">
        <ion-label>Used</ion-label>
        <ion-text slot="end" class="summary-value danger">{{ (profile?.expiredMiles || 0) + (profile?.usedMiles || 0) }}</ion-text>
      </ion-item>
    </ion-card>
  </div>
  
  <!-- Danger Zone -->
  <div class="danger-zone">
    <a [routerLink]="['/secure/profileremove']" class="danger-action">
      <ion-icon name="trash-outline"></ion-icon>
      <span>Remove Profile</span>
      <ion-icon name="chevron-forward-outline" class="arrow"></ion-icon>
    </a>
  </div>
</ion-content>