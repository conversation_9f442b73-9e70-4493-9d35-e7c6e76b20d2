/* Modern Dashboard Styles */

/* App Background */
.app-background {
  --background: #f5f7fa;
  position: relative;
}

/* Glass Balance Cards */
.glass-balance-cards {
  margin-top: -40px;
  padding: 0 24px;
  position: relative;
  z-index: 2;
  display: flex;
  gap: 16px;
  animation: slideUp 0.6s ease-out;
}

.glass-card {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
  position: relative;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:active {
    transform: translateY(2px);
    box-shadow: 0 4px 20px 0 rgba(31, 38, 135, 0.2);
  }

  &.primary {
    background: linear-gradient(135deg, var(--ion-color-primary, #FF6B35) 0%, #E55525 100%);
    color: white;
    border: none;
  }

  .card-content {
    position: relative;
    z-index: 1;
  }

  .card-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    opacity: 0.9;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .card-value {
    font-size: 26px;
    font-weight: 700;
    line-height: 1;
  }

  &.points .card-value {
    color: var(--ion-color-primary, #FF6B35);
  }
}

/* Modern Action Grid */
.modern-actions {
  padding: 32px 24px;
}

.actions-title {
  font-size: 20px;
  font-weight: 600;
  color: #212121;
  margin-bottom: 24px;
}

.modern-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.modern-action-card {
  background: white;
  border-radius: 24px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: 140px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  text-decoration: none;
  animation: scaleIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
  }

  /* Action card gradients */
  &.profile {
    background: linear-gradient(135deg, #FFE0B2 0%, #FFCC80 100%);
  }

  &.security {
    background: linear-gradient(135deg, #E1F5FE 0%, #B3E5FC 100%);
  }

  &.pools {
    background: linear-gradient(135deg, #E8F5E9 0%, #C8E6C9 100%);
  }

  &.points-btn {
    background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
  }

  /* Action icons */
  .action-icon {
    width: 48px;
    height: 48px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;

    ion-icon {
      font-size: 24px;
      color: white;
    }
  }

  &.profile .action-icon { background: #FF6F00; }
  &.security .action-icon { background: #0288D1; }
  &.pools .action-icon { background: #388E3C; }
  &.points-btn .action-icon { background: #7B1FA2; }

  .action-label {
    font-size: 16px;
    font-weight: 600;
    color: #212121;
    margin: 0;
  }

  /* Decorative element */
  .action-decoration {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
  }
}

/* Points Summary Section */
.points-summary {
  padding: 0 24px 24px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #212121;
  margin-bottom: 16px;
}

.summary-card {
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin: 0;
}

.summary-item {
  --padding-start: 20px;
  --padding-end: 20px;
  --inner-padding-end: 0;
  font-size: 16px;

  ion-label {
    font-weight: 500;
    color: #616161;
  }

  .summary-value {
    font-weight: 700;
    font-size: 18px;
    color: #212121;

    &.primary {
      color: var(--ion-color-primary, #FF6B35);
    }

    &.success {
      color: var(--ion-color-success, #4CAF50);
    }

    &.danger {
      color: var(--ion-color-danger, #F44336);
    }
  }

  &.clickable {
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
  }

  .nav-arrow {
    font-size: 18px;
    color: #BDBDBD;
    margin-left: 8px;
  }
}

/* Danger Zone */
.danger-zone {
  padding: 24px;
  margin-top: 24px;
}

.danger-action {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: white;
  border: 2px solid #FFEBEE;
  border-radius: 16px;
  color: var(--ion-color-danger, #F44336);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;

  ion-icon:first-child {
    font-size: 24px;
  }

  span {
    flex: 1;
    font-size: 16px;
  }

  .arrow {
    font-size: 18px;
    opacity: 0.5;
  }

  &:hover {
    background: #FFEBEE;
    border-color: var(--ion-color-danger, #F44336);
  }

  &:active {
    transform: scale(0.98);
  }
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive adjustments */
@media (max-width: 320px) {
  .glass-balance-cards {
    padding: 0 16px;
  }
  
  .glass-card {
    padding: 16px;
    
    .card-value {
      font-size: 22px;
    }
  }
  
  .modern-actions {
    padding: 24px 16px;
  }
  
  .modern-action-card {
    height: 120px;
    padding: 20px;
  }
  
  .points-summary {
    padding: 0 16px 16px;
  }
}

/* Legacy styles for compatibility */
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.w-full {
  width: 100%;
}