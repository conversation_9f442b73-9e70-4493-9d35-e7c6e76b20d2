import { Component, Injector, OnInit, ChangeDetectorRef } from '@angular/core';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
} from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-dashboard',
  templateUrl: 'dashboard.component.html',
  styleUrls: ['dashboard.component.scss'],
})
export class DashboardComponent extends AbstractComponent {
  profile?: MemberProfile;

  environment = environment;

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    private cd: ChangeDetectorRef,
    public lssConfig: LssConfig
  ) {
    super(injector);
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        if (this.profile) this.getBalance();
        this.detectChanges();
      })
    );
  }

  getBalance() {
    this.memberService
      .memberBalance(this.kc.lpUniueReference)
      .subscribe((data: any) => {
        this.profile = { ...this.profile, ...data };
      });
  }
}
