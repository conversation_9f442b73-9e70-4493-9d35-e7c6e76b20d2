<ion-content class="app-background">
  <lib-head-logo
    [names]="profile?.givenNames + ' ' + profile?.surname"
    [membership]="profile?.newMembershipNumber"
    type="membership"
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />

  <ion-card class="points-summary-card p-4">
    <ion-item>
      <ion-row class="ion-justify-content-between w-full">
        <ion-card-subtitle>{{ lssConfig.pointsTitle }}</ion-card-subtitle>
        <ion-text>{{ profile?.currentBalance! }}</ion-text>
      </ion-row>
    </ion-item>

    <ion-item>
      <ion-row class="ion-justify-content-between w-full">
        <ion-card-subtitle>Earned</ion-card-subtitle>
        <ion-text class="pcu-earned">{{ profile?.baseMiles! + profile?.bonusMiles! }}</ion-text>
      </ion-row>
    </ion-item>

    <ion-item>
      <ion-row class="ion-justify-content-between w-full">
        <ion-card-subtitle>Used</ion-card-subtitle>
        <ion-text class="pcu-spent">{{ profile?.expiredMiles! + profile?.usedMiles! }}</ion-text>
      </ion-row>
    </ion-item>
  </ion-card>

  <ion-card class="points-transfer-container p-4" *ngIf="profile?.newMembershipNumber">
    <lib-points-transfer 
      [membershipNumber]="profile?.newMembershipNumber || ''"
      (transferSuccess)="onTransferSuccess($event)"
      (transferError)="onTransferError($event)"
      containerClass="w-full max-w-md mx-auto bg-white rounded-lg"
      titleClass="text-xl font-bold mb-4 text-center"
      formGroupClass="mb-4 bg-blue-500 text-white p-2 rounded-lg"
      labelClass="block text-sm font-medium text-gray-700 mb-1"
      inputClass="w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
      buttonClass="w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
      errorClass="text-sm text-red-600 mt-1"
    ></lib-points-transfer>
  </ion-card>

</ion-content>
