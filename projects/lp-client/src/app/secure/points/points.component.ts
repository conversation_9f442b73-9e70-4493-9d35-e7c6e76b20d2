import { Component, Injector, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
  AccountPoolService
} from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';

@Component({
  selector: 'app-points',
  templateUrl: 'points.component.html',
  styleUrls: ['points.component.scss'],
})
export class PointsComponent extends AbstractComponent implements OnInit {
  profile?: MemberProfile;
   poolInfo: any = null;
   hasPoolInvite = false;
   isPoolLoading = false;
   poolError = '';
   
   // Add form for points transfer
   transferForm: FormGroup;
 
   constructor(
     injector: Injector,
     private memberService: MemberService,
     private kc: KeyCloakService,
     private accountPoolService: AccountPoolService,
     public lssConfig: LssConfig,
     private fb: FormBuilder
   ) {
     super(injector);
     // Initialize form to fix "transferForm.get is not a function" error
     this.transferForm = this.fb.group({
       points: ['', [Validators.required, Validators.min(1)]],
       recipient: ['', Validators.required]
     });
   }
 
   ngOnInit() {
     this.addGlobalSubscription(
       this.memberService.profileSubject.subscribe((data) => {
         this.profile = data;
         if (this.profile?.newMembershipNumber) {
           this.checkPoolInvite();
           this.findPool();
         }
         this.detectChanges();
       })
     );
   }
 
   onTransferSuccess(event: any) {
     this.presentToast({
       message: 'Points transferred successfully!',
       color: 'success',
       duration: 3000,
       position: 'bottom'
     });
     // Refresh profile data to get updated points balance
     this.refreshProfileData();
   }
 
   onTransferError(error: any) {
     this.presentToast({
       message: error?.error?.message || 'Failed to transfer points. Please try again.',
       color: 'danger',
       duration: 3000,
       position: 'bottom'
     });
   }
 
   onPoolInviteAccepted(event: any) {
     this.presentToast({
       message: 'Pool invitation accepted successfully!',
       color: 'success',
       duration: 3000,
       position: 'bottom'
     });
     this.hasPoolInvite = false;
     this.findPool(); // Refresh pool data
   }
 
   onPoolInviteDeclined(event: any) {
     this.presentToast({
       message: 'Pool invitation declined',
       color: 'medium',
       duration: 3000,
       position: 'bottom'
     });
     this.hasPoolInvite = false;
   }
 
   onPoolError(error: any) {
     this.presentToast({
       message: error?.error?.message || 'An error occurred with the account pool',
       color: 'danger',
       duration: 3000,
       position: 'bottom'
     });
   }
 
   private refreshProfileData() {
     if (this.kc.lpUniueReference) {
       this.memberService.memberBalance(this.kc.lpUniueReference).subscribe((data: any) => {
         this.profile = { ...this.profile, ...data };
         this.detectChanges();
       });
     }
   }
 
   private checkPoolInvite() {
     if (!this.profile?.newMembershipNumber) return;
 
     this.accountPoolService.checkInviteStatus(this.profile.newMembershipNumber).subscribe({
       next: (response: any) => {
         this.hasPoolInvite = response?.status === 'Y';
         this.detectChanges();
       },
       error: (error: any) => {
         // Gracefully handle the error - assume no invite if there's an error
         console.error('Failed to check pool invite status', error);
         this.hasPoolInvite = false;
         this.detectChanges();
       }
     });
   }
 
   private findPool() {
     if (!this.profile?.newMembershipNumber) return;
     
     this.isPoolLoading = true;
     this.poolError = '';
 
     this.accountPoolService.findPool(this.profile.newMembershipNumber).subscribe({
       next: (response: any) => {
         if (response) {
           // Improved date parsing logic
           if (response.BEGINDATE && typeof response.BEGINDATE === 'string') {
             try {
               // Better handling of the timezone format
               const dateStr = response.BEGINDATE.replace(/\[.*\]$/, '');
               const parsedDate = new Date(dateStr);
               
               // Verify the date is valid
               if (!isNaN(parsedDate.getTime())) {
                 response.BEGINDATE = parsedDate;
               } else {
                 console.error('Invalid date format:', response.BEGINDATE);
                 response.BEGINDATE = null;
               }
             } catch (e) {
               console.error('Error parsing date:', e);
               response.BEGINDATE = null;
             }
           }
           this.poolInfo = response;
         }
         this.isPoolLoading = false;
         this.detectChanges();
       },
       error: (error: any) => {
         this.isPoolLoading = false;
         // This is not necessarily an error, as the user might not belong to a pool
         if (error?.status !== 404) {
           this.poolError = error?.error?.message || 'Failed to load pool information';
         }
         this.detectChanges();
       }
     });
   }
 }
 