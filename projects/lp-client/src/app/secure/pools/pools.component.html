<ion-content class="app-background">
  <lib-head-logo
    [names]="profile?.givenNames + ' ' + profile?.surname"
    [membership]="profile?.newMembershipNumber"
    type="membership"
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />

  <!-- Pool Invitation Component -->
  <div *ngIf="hasPoolInvite" class="pool-invite-container">
    <lib-account-pool-invite 
      [membershipNumber]="profile?.newMembershipNumber || ''"
      (inviteAccepted)="onPoolInviteAccepted($event)"
      (inviteDeclined)="onPoolInviteDeclined($event)"
      (error)="onPoolError($event)"
    ></lib-account-pool-invite>
  </div>

  <!-- Account Pool Information -->
  <ion-card *ngIf="poolInfo" class="account-pool-card p-4">
    <ion-card-header>
      <ion-card-title>{{ poolInfo.POOLNAME }}</ion-card-title>
      <ion-card-subtitle>Account Pool</ion-card-subtitle>
    </ion-card-header>

    <ion-card-content>
      <ion-list>
        <ion-item>
          <ion-label>Pool Account Number</ion-label>
          <ion-note slot="end">{{ poolInfo.MPACC }}</ion-note>
        </ion-item>
        <ion-item>
          <ion-label>Status</ion-label>
          <ion-note slot="end">{{ formatStatus(poolInfo.STATUS) }}</ion-note>
        </ion-item>
        <ion-item>
          <ion-label>Total Points</ion-label>
          <ion-note slot="end">{{ poolInfo.TOTALUNITS }}</ion-note>
        </ion-item>
        <ion-item>
          <ion-label>Created</ion-label>
          <ion-note slot="end">{{ poolInfo.BEGINDATE ? (poolInfo.BEGINDATE | date:'mediumDate') : 'N/A' }}</ion-note>
        </ion-item>
      </ion-list>

      <ion-list-header>
        <ion-label>Members</ion-label>
      </ion-list-header>

      <table class="members-table">
        <thead>
          <tr>
            <th>Member</th>
            <th>Type</th>
            <th>Balance</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let member of poolInfo.members">
            <td>
              <div class="member-name-container">
                <div class="member-name">{{ member.NAME || 'Unknown' }}</div>
                <div class="member-id">{{ member.MPACC.trim() }}</div>
              </div>
            </td>
            <td>
              <span class="type-badge" [class.admin]="member.TYPE === 'ADMN'" [class.member]="member.TYPE !== 'ADMN'">
                {{ member.TYPE === 'ADMN' ? 'Administrator' : 'Member' }}
              </span>
            </td>
            <td>{{ member.BALANCE }}</td>
          </tr>
        </tbody>
      </table>
    </ion-card-content>
  </ion-card>

  <!-- Loading and Error States -->
  <div *ngIf="isPoolLoading" class="loading-container">
    <ion-spinner name="circles"></ion-spinner>
    <p>Loading pool information...</p>
  </div>
<!-- 
  <div *ngIf="poolError" class="error-container">
    <ion-icon name="alert-circle-outline"></ion-icon>
    <p>{{ poolError }}</p>
  </div> -->

  <ion-card class="account-pool-container p-4">
    <lib-account-pool 
      [membershipNumber]="profile?.newMembershipNumber || ''"
      (error)="onPoolError($event)"
    ></lib-account-pool>
  </ion-card>
</ion-content>
