.app-background {
  --background: var(--ion-color-base);
}

//   .card {
//     --background: var(--ion-color-primary-shade);
//     color: var(--ion-color-primary-contrast)
//   }
.center {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.account-name {
  font-size: 26px
}

.name-text {
  margin-left: 20px;
  font-size: 26px

}

.w-full {
  width: 100%;
}
.card-background {
  background-color: #fff;
  width: 100vw;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

// Add consistent card padding
ion-card {
  padding: 16px;
  margin: 16px;
  
  &.account-pool-card,
  &.account-pool-container {
    --padding-start: 16px;
    --padding-end: 16px;
    --padding-top: 16px;
    --padding-bottom: 16px;
    
    // Match bottom card text styling
    ion-card-title {
      font-size: 1.25rem !important;
      font-weight: 600 !important;
      color: var(--ion-color-dark) !important;
      text-align: center;
      margin-bottom: 1.5rem !important;
    }
    
    ion-card-subtitle {
      font-size: 0.875rem !important;
      color: var(--ion-color-medium) !important;
      text-align: center;
    }
    
    ion-list {
      background: transparent !important;
      padding: 0 !important;
      
      ion-item {
        --padding-start: 0px;
        --padding-end: 0px;
        --inner-padding-start: 0px;
        --inner-padding-end: 0px;
        --background: transparent;
        --border-color: transparent;
        margin-bottom: 1rem;
        
        ion-label {
          font-size: 0.875rem !important;
          font-weight: 500 !important;
          color: var(--ion-color-dark) !important;
        }
        
        ion-note {
          font-size: 0.875rem !important;
          font-weight: 400 !important;
          color: var(--ion-color-dark) !important;
        }
      }
    }
    
    ion-list-header {
      padding: 0 !important;
      margin-top: 1.5rem !important;
      margin-bottom: 1rem !important;
      
      ion-label {
        font-size: 1.125rem !important;
        font-weight: 500 !important;
        color: var(--ion-color-dark) !important;
      }
    }
  }
}

.transaction-summary {
  ion-row {
      h3 {
          margin-bottom: 1rem;
          margin-left: 16px;
          font-size: 1.4rem;
          font-weight: 400;
      }
  }
  
  ion-col {
      ion-row {
          h3 {
              font-size: 0.8rem;
              font-weight: 400;
              width: 100%;
              margin-right: 16px;

              span {
                  font-size: 1rem;
                  padding-bottom: 0;
              }
          }
      }
  }
  
  h3 {
      margin-bottom: 1rem;
      margin-left: 16px;

      span {
          font-size: 1rem;
          display: block;
          padding-bottom: 12px;
      }
  }
}

.pcu-earned {
  color: var(--ion-color-success) !important;
}

.pcu-spent {
  color: var(--ion-color-danger) !important;
}

.points-item {
  cursor: pointer;
  position: relative;
  
  &::after {
      content: '';
      position: absolute;
      right: 16px;
      top: 50%;
      transform: translateY(-50%);
      width: 8px;
      height: 8px;
      border-top: 2px solid var(--ion-color-medium);
      border-right: 2px solid var(--ion-color-medium);
      transform: translateY(-50%) rotate(45deg);
  }
  
  &:hover {
      background-color: rgba(var(--ion-color-primary-rgb), 0.1);
  }
}

table.members-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  table-layout: fixed;
  
  th {
    background-color: #f8f9fa;
    text-align: left;
    padding: 8px;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    font-size: 0.875rem; // Slightly larger to match general table font size
    color: #495057;
    
    &:first-child {
      width: 50%;
    }
    
    &:nth-child(2) {
      width: 25%;
      text-align: center;
    }
    
    &:nth-child(3) {
      width: 25%;
      text-align: right;
    }
  }
  
  td {
    padding: 8px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
    word-wrap: break-word;
    font-size: 0.875rem; // Base font size for td elements
    
    &:first-child {
      width: 50%;
    }
    
    &:nth-child(2) {
      width: 25%;
      text-align: center;
    }
    
    &:nth-child(3) {
      width: 25%;
      text-align: right;
      font-weight: 500;
      color: #28a745;
      font-size: 0.8rem;
    }
  }
  
  tbody tr {
    &:hover {
      background-color: #f8f9fa;
    }
    
    &:last-child td {
      border-bottom: none;
    }
  }
}

.member-name-container {
  display: flex;
  flex-direction: column;
  
  .member-name {
    font-weight: 500; // Changed from 600 to 500 for consistency
    color: #212529;
    margin-bottom: 0.125rem; // Reduced margin
    font-size: 0.875rem; // Slightly smaller to match table font
    line-height: 1.2;
  }
  
  .member-id {
    font-size: 0.7rem; // Slightly smaller
    color: #6c757d;
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 12px;
    display: inline-block;
    width: fit-content;
    font-family: monospace;
  }
}

.type-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  display: inline-block;
  line-height: 1.2;
  
  &.admin {
    background-color: #ff6b35;
    color: white;
  }
  
  &.member {
    background-color: #e9ecef;
    color: #495057;
  }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
  table.members-table {
    th, td {
      padding: 6px 4px;
      font-size: 0.7rem;
    }
    
    .member-name-container {
      .member-name {
        font-size: 0.8rem;
      }
      
      .member-id {
        font-size: 0.65rem;
        padding: 1px 4px;
      }
    }
    
    .type-badge {
      font-size: 0.65rem;
      padding: 2px 6px;
    }
  }
}