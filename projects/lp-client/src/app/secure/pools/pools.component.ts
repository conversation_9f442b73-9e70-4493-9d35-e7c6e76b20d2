import { Component, Injector, OnInit } from '@angular/core';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
  AccountPoolService
} from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-pools',
  templateUrl: 'pools.component.html',
  styleUrls: ['pools.component.scss'],
})
export class PoolsComponent extends AbstractComponent implements OnInit {
  profile?: MemberProfile;
  poolInfo: any = null;
  hasPoolInvite = false;
  isPoolLoading = false;
  poolError = '';
  
  // Add form for points transfer
  transferForm: FormGroup;

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    private accountPoolService: AccountPoolService,
    public lssConfig: LssConfig,
    private fb: FormBuilder
  ) {
    super(injector);
    // Initialize form to fix "transferForm.get is not a function" error
    this.transferForm = this.fb.group({
      points: ['', [Validators.required, Validators.min(1)]],
      recipient: ['', Validators.required]
    });
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        if (this.profile?.newMembershipNumber) {
          // Don't show errors initially - check pool status gracefully
          this.checkPoolInvite();
          this.findPool();
        }
        this.detectChanges();
      })
    );
  }

  onTransferSuccess(event: any) {
    this.presentToast({
      message: 'Points transferred successfully!',
      color: 'success',
      duration: 3000,
      position: 'bottom'
    });
    // Refresh profile data to get updated points balance
    this.refreshProfileData();
  }

  onTransferError(error: any) {
    this.presentToast({
      message: error?.error?.message || 'Failed to transfer points. Please try again.',
      color: 'danger',
      duration: 3000,
      position: 'bottom'
    });
  }

  onPoolInviteAccepted(event: any) {
    this.presentToast({
      message: 'Pool invitation accepted successfully!',
      color: 'success',
      duration: 3000,
      position: 'bottom'
    });
    this.hasPoolInvite = false;
    this.findPool(); // Refresh pool data
  }

  onPoolInviteDeclined(event: any) {
    this.presentToast({
      message: 'Pool invitation declined',
      color: 'medium',
      duration: 3000,
      position: 'bottom'
    });
    this.hasPoolInvite = false;
  }

  onPoolError(error: any) {
    this.presentToast({
      message: error?.error?.message || 'An error occurred with the account pool',
      color: 'danger',
      duration: 3000,
      position: 'bottom'
    });
  }

  private refreshProfileData() {
    if (this.kc.lpUniueReference) {
      this.memberService.memberBalance(this.kc.lpUniueReference).subscribe((data: any) => {
        this.profile = { ...this.profile, ...data };
        this.detectChanges();
      });
    }
  }

  private checkPoolInvite() {
    if (!this.profile?.newMembershipNumber) return;

    this.accountPoolService.checkInviteStatus(this.profile.newMembershipNumber).subscribe({
      next: (response: any) => {
        this.hasPoolInvite = response?.status === 'Y';
        this.detectChanges();
      },
      error: (error: any) => {
        // Gracefully handle the error - assume no invite if there's an error
        console.error('Failed to check pool invite status', error);
        this.hasPoolInvite = false;
        this.detectChanges();
      }
    });
  }

  /**
   * Formats status code to human readable text
   * @param status The status code to format
   * @returns Formatted status text
   */
  formatStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'STAA': 'Active',
      'INAC': 'Inactive',
      'PEND': 'Pending',
      'SUSP': 'Suspended'
    };
    return statusMap[status] || status;
  }

  private findPool() {
    if (!this.profile?.newMembershipNumber) return;
    
    this.isPoolLoading = true;
    this.poolError = '';

    this.accountPoolService.findPool(this.profile.newMembershipNumber).subscribe({
      next: (response: any) => {
        if (response) {
          // Log all pool information
          console.log('=== POOL INFORMATION RETRIEVED ===');
          console.log('Full Pool Response:', response);
          console.log('Pool Account Number (MPACC):', response.MPACC);
          console.log('Pool Entity ID:', response.ENTITYID);
          console.log('Pool Name:', response.POOLNAME);
          console.log('Pool Status:', response.STATUS);
          console.log('Total Units:', response.TOTALUNITS);
          console.log('Pool Split:', response.POOLSPLIT);
          console.log('CIS:', response.CIS);
          console.log('Account Alias:', response.ACCALIAS);
          console.log('Language:', response.LANGUAGE);
          console.log('Email:', response.EMAIL);
          console.log('Contact Number:', response.CONTACTNUMBER);
          console.log('Begin Date:', response.BEGINDATE);
          console.log('End Date:', response.ENDDATE);
          console.log('Audit User:', response.AUDITUSER);
          console.log('Audit Date:', response.AUDITDATE);
          
          if (response.members && response.members.length > 0) {
            console.log('=== POOL MEMBERS ===');
            response.members.forEach((member: any, index: number) => {
              console.log(`Member ${index + 1}:`, {
                'Account': member.MPACC,
                'Name': member.NAME,
                'Type': member.TYPE,
                'Balance': member.BALANCE,
                'Status': member.MEMBERSTATUS,
                'Privacy': member.PRIVACY,
                'Allow Activity': member.ALLOWACTIVITY,
                'Allow Award': member.ALLOWAWARD,
                'Invite Status': member.INVITESTATUS,
                'Account Alias': member.ACCALIAS,
                'Audit User': member.AUDITUSER,
                'Audit Date': member.AUDITDATE,
                'Actions': member.ACTIONS
              });
            });
          }
          console.log('=================================');
          
          // Improved date parsing logic
          if (response.BEGINDATE && typeof response.BEGINDATE === 'string') {
            try {
              // Better handling of the timezone format
              const dateStr = response.BEGINDATE.replace(/\[.*\]$/, '');
              const parsedDate = new Date(dateStr);
              
              // Verify the date is valid
              if (!isNaN(parsedDate.getTime())) {
                response.BEGINDATE = parsedDate;
              } else {
                console.error('Invalid date format:', response.BEGINDATE);
                response.BEGINDATE = null;
              }
            } catch (e) {
              console.error('Error parsing date:', e);
              response.BEGINDATE = null;
            }
          }
          this.poolInfo = response;
        }
        this.isPoolLoading = false;
        this.detectChanges();
      },
      error: (error: any) => {
        this.isPoolLoading = false;
        // Don't show error for 404 - user simply doesn't belong to a pool yet
        if (error?.status === 404) {
          // User doesn't belong to a pool - this is a normal state
          this.poolInfo = null;
          this.poolError = '';
        } else if (error?.status !== 404) {
          // Only show error for actual errors, not for 404
          this.poolError = error?.error?.message || 'Failed to load pool information';
          console.error('Error loading pool information', error);
        }
        this.detectChanges();
      }
    });
  }
}
