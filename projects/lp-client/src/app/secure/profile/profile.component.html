<ion-content class="app-background">
  <lib-head-logo
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />

  <ion-card class="">
    <form [formGroup]="profileForm" (ngSubmit)="doLoad()">
      <ion-item lines="none" *ngIf="isFormComponentInvalid('membershipNumber')">
        <div
          *ngFor="
            let error of getComponentErrors('membershipNumber');
            let i = index
          "
          class="validator-error w-full"
        >
          <div *ngIf="i == 0" class="w-full">
            Card number must be 16 numeric characters long.
          </div>
          <!-- {{ error }} -->
        </div>
      </ion-item>
      <ion-item>
        <ion-icon slot="start" name="ribbon-outline"></ion-icon>

        <ion-select
          label="Title"
          labelPlacement="floating"
          formControlName="title"
          placeholder="Please select.."
        >
          <ion-select-option
            *ngFor="let codeItem of getCodeList('TITL') | async"
            [value]="codeItem.codeId"
            >{{ codeItem.description }}</ion-select-option
          >
        </ion-select>
      </ion-item>
      <ion-item>
        <ion-icon slot="start" name="person"></ion-icon>

        <!-- <ion-label position="floating">Firstname</ion-label> -->
        <ion-input
          [readonly]="status_loading"
          labelPlacement="floating"
          label="* Firstname"
          fill="solid"
          type="text"
          formControlName="givenNames"
        ></ion-input>
      </ion-item>
      <ion-item
        *ngIf="!form.givenNames.valid && form.givenNames.touched"
        class="validator-error"
      >
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.givenNames.errors,
              'Firstname'
            )
          "
        >
          {{ error }}
        </div>
      </ion-item>
      <ion-item>
        <ion-icon slot="start" name="person"></ion-icon>

        <!-- <ion-label position="floating">Lastname:*</ion-label> -->
        <ion-input
          labelPlacement="floating"
          label="* Surname"
          type="text"
          fill="solid"
          formControlName="surname"
          [readonly]="status_loading"
        ></ion-input>
      </ion-item>
      <ion-item
        *ngIf="!form.surname.valid && form.surname.touched"
        class="validator-error"
      >
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.surname.errors,
              'Lastname'
            )
          "
        >
          {{ error }}
        </div>
      </ion-item>

      <ion-item>
        <ion-icon slot="start" name="id-card-outline"></ion-icon>
        <ion-input
          label="ID Type"
          labelPlacement="floating"
          [value]="idType === 'nationalId' ? 'South African ID' : 'Passport'"
          readonly
        ></ion-input>
      </ion-item>

      <ion-item *ngIf="idType === 'nationalId' || !idType">
        <ion-icon slot="start" name="id-card-outline"></ion-icon>
        <ion-input
          labelPlacement="floating"
          label="South African ID number"
          type="text"
          formControlName="nationalIdNum"
          fill="solid"
          [readonly]="true"
        >
        </ion-input>
      </ion-item>
      <ion-item *ngIf="!form.nationalIdNum.valid && form.nationalIdNum.touched && (idType === 'nationalId' || !idType)">
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.nationalIdNum.errors,
              'ID number'
            )
          "
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>

      <ion-item *ngIf="idType === 'passport'">
        <ion-icon slot="start" name="id-card-outline"></ion-icon>
        <ion-input
          labelPlacement="floating"
          label="Passport number"
          type="text"
          formControlName="passortNum"
          fill="solid"
          [readonly]="true"
        >
        </ion-input>
      </ion-item>
      <ion-item *ngIf="!form.passortNum.valid && form.passortNum.touched && idType === 'passport'">
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.passortNum.errors,
              'Passport number'
            )
          "
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>

      <!-- Updated Country of Origin field for passport -->
      <ion-item *ngIf="idType === 'passport'">
        <ion-icon slot="start" name="flag-outline"></ion-icon>
        <ion-input
          label="* Country of Origin"
          labelPlacement="floating"
          [value]="selectedCountryName"
          readonly
        ></ion-input>
      </ion-item>

      <!-- New Passport Expiry Date field for passport -->
      <ion-item *ngIf="idType === 'passport'" lines="inset">
        <ion-icon slot="start" name="calendar-outline"></ion-icon>
        <ion-input
          label="* Passport Expiry Date"
          labelPlacement="floating"
          formControlName="expiryDate"
          type="date"
          [min]="todaysDate()"
          [readonly]="true"
        ></ion-input>
      </ion-item>
      <ion-item *ngIf="!form.expiryDate?.valid && form.expiryDate?.touched && idType === 'passport'">
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.expiryDate?.errors,
              'Passport Expiry Date'
            )
          "
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>

      <ion-item id="open-date-input" lines="inset">
        <ion-icon slot="start" name="calendar-outline"></ion-icon>
        <ion-input
          label="Birth Date"
          labelPlacement="floating"
          formControlName="birthDate"
          type="date"
          [max]="todaysDate12YearsAgo()"
        ></ion-input>
      </ion-item>
      <ion-item lines="none" *ngIf="isFormComponentInvalid('birthDate')">
        <div
          *ngFor="let error of getComponentErrors('birthDate')"
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>
      <ion-item>
        <ion-icon slot="start" name="balloon-outline"></ion-icon>

        <ion-select
          label="Gender"
          labelPlacement="floating"
          formControlName="gender"
          placeholder="Please select.."
        >
          <ion-select-option
            *ngFor="let codeItem of getCodeList('SEX') | async"
            [value]="codeItem.codeId"
            >{{ codeItem.description }}</ion-select-option
          >
        </ion-select>
      </ion-item>
      <div class="form-spacer"></div>
      <lp-pos-address
        type="POST"
        [mainAddress]="addr"
        [mainForm]="profileForm"
        #address_post
        [required_field]="true"

      ></lp-pos-address>
      <ion-item lines="inset" class="ion-intl-tel item-has-value">
        <ion-icon slot="start" name="phone-portrait-outline"></ion-icon>


          <ion-input
          labelPlacement="floating"
          label="* Mobile Number:"
          type="text"
          [value]="phoneTogether"
          disabled
        ></ion-input>

        <!-- <ion-label position="floating">Mobile Number: *</ion-label>
        <ion-intl-tel-input
          id="phone-number"
          name="phone-number"
          formControlName="phone"
          labelPlacement="floating"
          label="Mobile Number: *"
          [enableAutoCountrySelect]="true"
          [selectFirstCountry]="
            lssConfig.telephone.selectFirstCountry
          "
          [preferredCountries]="
            lssConfig.telephone.preferredCountries
          "
        >
        </ion-intl-tel-input> -->
      </ion-item>
      <ion-item *ngIf="!form.phone.valid && form.phone.touched">
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.phone.errors,
              'Mobile Number'
            )
          "
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>
      <lib-stores
        [favourite_id]="favourite_id"
        (updateDataEvent)="updateAddress($event)"
        [required_field]="true"
      />

      <ion-item>
        <ion-icon slot="start" name="mail"></ion-icon>

        <!-- <ion-label position="floating">Email:</ion-label> -->
        <ion-input
          labelPlacement="floating"
          label="Email"
          type="email"
          formControlName="emailAddress"
        >
        </ion-input>
      </ion-item>
      <ion-item *ngIf="!form.emailAddress.valid && form.emailAddress.touched">
        <div
          *ngFor="
            let error of _formValidations.doErrors(
              form.emailAddress.errors,
              'Email'
            )
          "
          class="validator-error"
        >
          {{ error }}
        </div>
      </ion-item>
      <div class="form-spacer"></div>
      <!-- <ion-button [disabled]="!isValid" expand="block" class="save" type="submit" >Save</ion-button> -->
      <ion-button expand="block" class="save" type="submit">Save</ion-button>

      <!-- <ion-button expand="block" type="submit" [disabled]="!profileForm.valid || loading">Save</ion-button> -->
    </form>
  </ion-card>
</ion-content>
