.app-background {
    --background: var(--ion-color-base)
}
.action-card {
    text-align: center;
	
	background-color: var(--ion-color-base-shade);
	border-radius: 1rem;
	box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);
}
.card-background {
    background-color: #fff;
    width: 100vw;
    padding: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.text-input[readonly] {
    --background: #7c7474;
    pointer-events: none;
    touch-action: none;
  }
.formbackground {
    --background: var(--ion-color-base-shade);
    color: var(--ion-color-base-contrast);
    height: 70vh
   }
form {
    background-color: #fff;
    padding: 6px
}
.center {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.action-icon {
	position: relative;
	top: -25px;
    right: -23px;
	border-radius: 3rem;
	width: 4rem;
	height: 4rem;
	box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.4);
    text-align: center;
    padding: 25px 0;
	background-color: var(--ion-color-tertiary);

}
.points {
	--background: var(--ion-color-secondary);
    width: 100%;
    margin-left: 20px;
    height: 50px;

}

.save {
	--background: var(--ion-color-primary);

}
.logo {
    height: 100px
}
.title {
    margin-left: 20px;
    margin-bottom: 14px;
}
.action-name {
	position: relative;

    top: -20px
}

ion-modal {
    --width: 290px;
    --height: 392px;
    --border-radius: 8px;
  }

  ion-modal ion-datetime {
    height: 392px;
  }

  .calendar-day {
    --color: black !important;
    color: black !important;
    --background: white !important;
    background: white !important;
  }