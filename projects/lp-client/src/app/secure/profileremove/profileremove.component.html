<ion-content class="app-background" *ngIf="!showGoHome">

  <lib-head-logo type="phone" [phone]="lssConfig.pages.contact.callCenter" [src]="lssConfig.pages.landing.loggedinIcon" />
    <ion-card-content class="formbackground">
    <form [formGroup]="_form">
      <ion-item>
        <ion-textarea  labelPlacement="floating"
        label="Reason for Deleting"
        type="text"
        formControlName="message" placeholder="Reason for Deleting"></ion-textarea>
      </ion-item>
      <ion-button expand="block" class="submit"  (click)="setOpen(true)">Delete Account</ion-button>
       <p class="text-red px-6" *ngIf="showError" >
        The provided mobile number does not match the one associated with this profile. Account deletion cannot proceed.
       </p> 
    
    </form>
  </ion-card-content>




  <ion-modal [isOpen]="isModalOpen">
    <ng-template>
      <ion-header>
        <ion-toolbar>
          <ion-title>Delete Account</ion-title>
          <ion-buttons slot="end">
            <ion-button (click)="setOpen(false)">Close</ion-button>
          </ion-buttons>
        </ion-toolbar>
      </ion-header>
      <ion-content>
        <p class="text-black  px-6">
            In order to delete your account, please be aware that this action is irreversible and will result in the permanent removal of all your personal data from our system. This includes any stored information, preferences, and transaction history associated with your account. Once deleted, we won't be able to recover any of this data, so please proceed with caution. If you're certain about your decision, you can proceed with the account deletion process. Thank you for using our app, and we hope to serve you again in the future if you decide to return.</p>
        <ion-item lines="inset" class="ion-intl-tel item-has-value">
            <ion-label position="floating">*Please fill in your account Mobile Number in order to delete:</ion-label>
            <ion-intl-tel-input id="phone-number" labelPlacement="floating" label="* Mobile Number:" name="phone-number" [(ngModel)]="phone" [enableAutoCountrySelect]="true" [selectFirstCountry]="lssConfig.telephone.selectFirstCountry" [preferredCountries]="lssConfig.telephone.preferredCountries"></ion-intl-tel-input>
        </ion-item>
      <ion-button expand="block" class="submit"  (click)="submit()">Delete Account</ion-button>

      </ion-content>
    </ng-template>
  </ion-modal>
</ion-content>


<ion-content class="app-background" *ngIf="showGoHome">

  <lib-head-logo type="phone" [phone]="lssConfig.contact.callCenter" [src]="lssConfig.pages.landing.loggedinIcon" />
    <ion-card-content class="formbackground">
  
       <p class="px-6" >
        We are sorry to see you go!
       </p> 
      <ion-button expand="block" class="submit"  [routerLink]="['/public/validate']">Sign Up Again</ion-button>

    
  </ion-card-content>
</ion-content>