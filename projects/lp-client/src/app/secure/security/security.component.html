<ion-content class="app-background">
  <lib-head-logo
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />

  <ion-card class="">
    <form [formGroup]="profileForm" (ngSubmit)="doLoad()">
      <ion-item>
        <ion-icon slot="start" name="keypad-outline"></ion-icon>
        <ion-input
          labelPlacement="floating"
          label="* Pin"
          type="text"
          formControlName="pin"
        ></ion-input>
      </ion-item>
      <ion-item *ngIf="isFormComponentInvalid('pin')">
        <div
          *ngFor="let error of getComponentErrors('pin')"
          class="validator-error"
        >
          <!-- {{ error }} -->
          5 Digits needed when you spend points.
        </div>
      </ion-item>

      <div class="form-spacer"></div>
      <!-- <ion-button [disabled]="!isValid" expand="block" class="save" type="submit" >Save</ion-button> -->
      <ion-button expand="block" class="save" type="submit"
        >Update Pin</ion-button
      >
    </form>
  </ion-card>

  <ion-card class="">
    <form [formGroup]="profileFormPassword" (ngSubmit)="doPassword()">
      <!-- <ion-button [disabled]="!isValid" expand="block" class="save" type="submit" >Save</ion-button> -->
      <ion-button expand="block" class="save" type="submit"
        >Update Password</ion-button
      >
    </form>
  </ion-card>
</ion-content>
