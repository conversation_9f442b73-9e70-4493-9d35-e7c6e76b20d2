<div class="transactions-container">
  <lib-head-logo
    [balance]="profile?.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />

  <div class="content-wrapper">
    <div class="card">
      <!-- Filter Controls -->
      <div class="filter-controls">
        <div class="chip-container">
          <button 
            class="chip"
            [class.active]="selectedTransactionType === 'All'"
            (click)="selectTransactionType('All')">
            All
          </button>
          <button 
            class="chip success"
            [class.active]="selectedTransactionType === 'Accrual'"
            (click)="selectTransactionType('Accrual')">
            Accrual
          </button>
          <button 
            class="chip danger"
            [class.active]="selectedTransactionType === 'Redemption'"
            (click)="selectTransactionType('Redemption')">
            Redemption
          </button>
          <button 
            class="chip warning"
            [class.active]="selectedTransactionType === 'Refund'"
            (click)="selectTransactionType('Refund')">
            Refund
          </button>
          <button 
            class="chip warning"
            [class.active]="selectedTransactionType === 'RefundRedemption'"
            (click)="selectTransactionType('RefundRedemption')">
            Refund Redemption
          </button>
          <button 
            class="chip dark"
            [class.active]="selectedTransactionType === 'Reversals'"
            (click)="selectTransactionType('Reversals')">
            Reversals
          </button>
        </div>
      </div>
      
      <div class="no-transactions" *ngIf="filteredStatements.length === 0">
        No transactions recorded yet.
      </div>

      <div class="transactions-list" *ngIf="filteredStatements.length > 0">
        <div
          class="transaction-item"
          [class.active-class]="i % 2 != 0"
          [class.inactive-class]="i % 2 == 0"
          *ngFor="let statement of filteredStatements; let i = index">
          
          <div class="transaction-icon">
            <i [class]="'icon-' + getTransactionIcon(statement.transactionType)" 
               [style.color]="getTransactionIconColor(statement.transactionType)">
            </i>
          </div>
          
          <div class="transaction-content">
            <div class="transaction-header">
              <h3>
                {{ statement.transactionType }}
                <span *ngIf="isReversalTransaction(statement)" class="badge">REV</span>
              </h3>
            </div>
            <p class="transaction-label">{{ statement.label }}</p>
          </div>
          
          <div class="transaction-details">
            <p *ngIf="statement.loadDate" class="transaction-date">
              {{ statement.loadDate.split("T")[0] }}
            </p>
            <p [class]="'transaction-points ' + getTransactionPointsClass(statement.transactionType)">
              {{ getTransactionPointsDisplay(statement) }}
            </p>
            <p *ngIf="statement.actValue" class="transaction-value">
              R {{ statement.actValue.toFixed(2) }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>