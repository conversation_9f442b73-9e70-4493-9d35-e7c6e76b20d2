.transactions-container {
  height: 100vh;
  overflow-y: auto;
  background: #0072bc !important;
  
  .content-wrapper {
    padding: 1rem;
    
    .card {
      background: white;
      border-radius: 12px;
      padding: 1rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .filter-controls {
        margin-bottom: 1rem;
        
        .chip-container {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5rem;
          
          .chip {
            padding: 0.5rem 1rem;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            background: white;
            color: #666;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.2s ease;
            
            &:hover {
              background: #f5f5f5;
            }
            
            &.active {
              background: var(--ion-color-primary);
              color: white;
              border-color: var(--ion-color-primary);
            }
            
            &.success.active {
              background: #28a745;
              border-color: #28a745;
            }
            
            &.danger.active {
              background: #dc3545;
              border-color: #dc3545;
            }
            
            &.warning.active {
              background: #ffc107;
              border-color: #ffc107;
              color: #000;
            }
            
            &.dark.active {
              background: #343a40;
              border-color: #343a40;
            }
          }
        }
      }
      
      .no-transactions {
        text-align: center;
        color: #666;
        font-style: italic;
        padding: 2rem;
      }
      
      .transactions-list {
        .transaction-item {
          display: flex;
          align-items: center;
          padding: 1rem;
          border-bottom: 1px solid #f0f0f0;
          
          &:last-child {
            border-bottom: none;
          }
          
          &.active-class {
            background: #f8f9fa;
          }
          
          .transaction-icon {
            margin-right: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #f0f0f0;
            
            i {
              font-size: 1.2rem;
              
              // Icon mappings for Ionicons names
              &.icon-add-circle-outline::before {
                content: '+';
                font-weight: bold;
                font-size: 1.5rem;
              }
              
              &.icon-remove-circle-outline::before {
                content: '−';
                font-weight: bold;
                font-size: 1.5rem;
              }
              
              &.icon-refresh-circle-outline::before {
                content: '↻';
                font-size: 1.2rem;
              }
              
              &.icon-arrow-undo-circle-outline::before {
                content: '↶';
                font-size: 1.2rem;
              }
              
              &.icon-gift-outline::before {
                content: '🎁';
                font-size: 1rem;
              }
              
              &.icon-pricetags-outline::before {
                content: '🏷';
                font-size: 1rem;
              }
            }
          }
          
          .transaction-content {
            flex: 1;
            margin-right: 1rem;
            
            .transaction-header {
              h3 {
                margin: 0 0 0.25rem 0;
                font-size: 1rem;
                font-weight: 600;
                color: #333;
                
                .badge {
                  background: #343a40;
                  color: white;
                  padding: 0.125rem 0.375rem;
                  border-radius: 4px;
                  font-size: 0.75rem;
                  margin-left: 0.5rem;
                }
              }
            }
            
            .transaction-label {
              margin: 0;
              font-size: 0.875rem;
              color: #666;
            }
          }
          
          .transaction-details {
            text-align: right;
            
            p {
              margin: 0 0 0.25rem 0;
              font-size: 0.875rem;
              
              &.transaction-date {
                color: #666;
              }
              
              &.transaction-points {
                font-weight: 600;
                
                &.pcu-earned {
                  color: #28a745;
                }
                
                &.pcu-spent {
                  color: #dc3545;
                }
                
                &.pcu-refund {
                  color: #ffc107;
                }
                
                &.pcu-reversal {
                  color: #343a40;
                }
              }
              
              &.transaction-value {
                color: #666;
                font-size: 0.75rem;
              }
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .transactions-container {
    .content-wrapper {
      padding: 0.5rem;
      
      .card {
        padding: 0.75rem;
        
        .filter-controls .chip-container .chip {
          padding: 0.375rem 0.75rem;
          font-size: 0.8rem;
        }
        
        .transactions-list .transaction-item {
          padding: 0.75rem;
          
          .transaction-icon {
            width: 35px;
            height: 35px;
            margin-right: 0.75rem;
          }
          
          .transaction-content {
            margin-right: 0.75rem;
            
            .transaction-header h3 {
              font-size: 0.9rem;
            }
            
            .transaction-label {
              font-size: 0.8rem;
            }
          }
          
          .transaction-details p {
            font-size: 0.8rem;
          }
        }
      }
    }
  }
}