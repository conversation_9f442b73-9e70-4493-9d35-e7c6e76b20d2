.app-background {
    --background: var(--ion-color-base)
}
.action-card {
    text-align: center;
	
	background-color: var(--ion-color-primary-shade);
	border-radius: 1rem;
	box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);
}
.card-background {
    background-color: #fff;
    width: 100vw;
    padding: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.center {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}
.action-icon {
	position: relative;
	top: -25px;
    right: -23px;
	border-radius: 3rem;
	width: 4rem;
	height: 4rem;
	box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.4);
    text-align: center;
    padding: 25px 0;
	background-color: var(--ion-color-tertiary);

}
.points {
	--background: var(--ion-color-secondary);
    width: 100%;
    margin-left: 20px;
    height: 50px;

}
.logo {
    height: 100px
}
.title {
    margin-left: 20px;
    margin-bottom: 14px;
}
.action-name {
	position: relative;

    top: -20px
}


ion-list {
    background: none;
    padding-top: 0;

    ion-item {
        --background: none;
    }
}

.transaction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.transaction-history {
    ion-icon {
        font-size: 2em;
    }

    h3 {
        font-size: 16px;
    }

    p {
        font-size: 14px;
    }
}

.pcu-earned {
    color: var(--ion-color-success) !important;
}

.pcu-spent {
    color: var(--ion-color-danger) !important;
}

.transaction-summary {
    ion-row {
        h3 {
            margin-bottom: 1rem;
            margin-left: 16px;
            font-size: 1.4rem;
            font-weight: 400;
        }
    }
    
    ion-col {
        ion-row {
            h3 {
                font-size: 0.8rem;
                font-weight: 400;
                width: 100%;
                margin-right: 16px;

                span {
                    font-size: 1rem;
                    padding-bottom: 0;
                }
            }
        }
    }
    
    h3 {
        margin-bottom: 1rem;
        margin-left: 16px;

        span {
            font-size: 1rem;
            display: block;
            padding-bottom: 12px;
        }
    }
}