import { Component, Injector, OnInit } from '@angular/core';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
} from 'lp-client-api';
import { AbstractComponent } from 'mobile-components';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'app-virtualcard',
  templateUrl: 'virtualcard.component.html',
  styleUrls: ['virtualcard.component.scss'],
})
export class VirtualCardComponent extends AbstractComponent {
  profile?: MemberProfile;

  environment = environment;
  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    public lssConfig: LssConfig
  ) {
    super(injector);
  }

  ngOnInit() {
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        this.loading = false;
        this.detectChanges();
      })
    );
  }
}
