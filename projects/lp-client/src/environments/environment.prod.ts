export const environment = {
  production: true,
  env: 'LIVE',
  client: 'rmic',
  lssConfig: {
    appCode: 'start',
    appName: 'Loyalty Client App',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    loadIdentity: false,
    identityBaseUrl: 'https://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'https://alpine',
    apiBaseUrl: 'https://rziaqa.loyaltyplus.aero/',
    configAPIUrl: 'https://{hostname}/config/api/v1/',
    defaultLat: -25.7461,
    defaultLng: 28.1881,
    apiId: 'MICA',
    memberPhone: {
      dialCode: '+27',
      nationalNumber: '',
    },
    memberCard: '',
    telephone: {
      selectFirstCountry: true,
      preferredCountries: ['za'],
      onlyCountries: ['za', 'ls', 'bw', 'na', 'sz', 'mz'],
    },
    pages: {
      landing: {
        loggedinIcon: 'assets/images/logo.png'
      }
    },
    authConfig: {
      // Url of the Identity Provider
      issuer: 'https://authqa.loyaltyplus.aero/auth/realms/AgriBonus',
      redirectUri: location.origin + '/lp-pos', //window.location.href.substring(0, window.location.href.lastIndexOf('/')),
      responseType: 'code',
      clientId: 'mobile-app',
      scope: 'openid profile email offline_access',
      requireHttps: false,
      logoutUrl: '/',
      url: 'https://authqa.loyaltyplus.aero/auth',
      realm: 'ZIADA',
      initOptions: {
        adapter: 'capacitor-native',
        responseMode: 'query',
        redirectUri: 'micaloyalty://callback',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          'micaloyalty://silent-check-sso',
      },
    },
  },
};
