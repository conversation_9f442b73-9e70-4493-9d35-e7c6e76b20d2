<div *ngIf="hasInvite" [ngClass]="containerClass">
  <h2 [ngClass]="titleClass">Account Pool Invitation</h2>
  
  <div *ngIf="isLoading" class="text-center text-gray-500 my-4">Loading...</div>
  <p *ngIf="errorMessage" class="text-sm text-red-600 mt-1">{{ errorMessage }}</p>
  <p *ngIf="successMessage" class="text-sm text-green-600 mt-1">{{ successMessage }}</p>

  <div *ngIf="poolInfo && !isLoading && !successMessage" class="mb-4">
    <p class="mb-2">You have been invited to join the account pool:</p>
    <p class="font-semibold">{{ poolInfo.POOLNAME }}</p>
    <p class="text-sm text-gray-600 mb-4">Pool ID: {{ poolInfo.ENTITYID }}</p>
    
    <div class="flex mt-4">
      <button [ngClass]="buttonClass" (click)="acceptInvite()">Accept Invitation</button>
      <button [ngClass]="cancelButtonClass" (click)="declineInvite()">Decline</button>
    </div>
  </div>
</div>
