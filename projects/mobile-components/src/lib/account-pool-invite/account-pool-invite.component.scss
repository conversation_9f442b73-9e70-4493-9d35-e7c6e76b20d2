/* Component-specific styles */
:host {
  display: block;
  margin: 1rem 0;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ion-color-dark);
  margin-bottom: 0.75rem;
}

.card-subtitle {
  font-size: 1rem;
  font-weight: 500;
  color: var(--ion-color-medium-shade);
  margin-bottom: 1rem;
}

p, .description-text {
  font-size: 0.875rem;
  color: var(--ion-color-dark);
  line-height: 1.5;
  margin-bottom: 1rem;
}

.invite-details {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--ion-color-light);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

button {
  width: 100%;
  padding: 0.75rem 1rem;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  
  &.accept-btn {
    background-color: var(--ion-color-success);
    color: white;
    margin-bottom: 0.75rem;
    
    &:hover {
      background-color: var(--ion-color-success-shade);
    }
  }
  
  &.decline-btn {
    background-color: var(--ion-color-danger);
    color: white;
    
    &:hover {
      background-color: var(--ion-color-danger-shade);
    }
  }
  
  &:active:not(:disabled) {
    transform: translateY(1px);
  }
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}
