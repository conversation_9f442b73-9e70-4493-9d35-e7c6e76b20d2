import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AccountPoolService } from 'lp-client-api';

interface InviteResponse {
  status: string;
}

interface PoolResponse {
  ENTITYID: number;
  POOLNAME: string;
  [key: string]: any;
}

interface ApiError {
  error?: {
    message?: string;
  };
  [key: string]: any;
}

@Component({
  selector: 'lib-account-pool-invite',
  templateUrl: './account-pool-invite.component.html',
  styleUrls: ['./account-pool-invite.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class AccountPoolInviteComponent implements OnInit {
  @Input() membershipNumber!: string;
  @Input() containerClass = 'w-full max-w-md mx-auto p-4 bg-white rounded-lg shadow';
  @Input() titleClass = 'text-xl font-bold mb-4';
  @Input() buttonClass = 'py-2 px-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 mr-2';
  @Input() cancelButtonClass = 'py-2 px-4 bg-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50';
  
  @Output() inviteAccepted = new EventEmitter<any>();
  @Output() inviteDeclined = new EventEmitter<any>();
  @Output() error = new EventEmitter<any>();

  hasInvite = false;
  poolInfo: any = null;
  isLoading = false;
  errorMessage = '';
  successMessage = '';

  constructor(private accountPoolService: AccountPoolService) { }

  ngOnInit(): void {
    this.checkForInvite();
  }

  checkForInvite(): void {
    if (!this.membershipNumber) {
      return;
    }

    this.isLoading = true;

    this.accountPoolService.checkInviteStatus(this.membershipNumber).subscribe({
      next: (response: InviteResponse) => {
        this.hasInvite = response?.status === 'Y';
        
        if (this.hasInvite) {
          this.findPoolDetails();
        } else {
          this.isLoading = false;
        }
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to check invite status');
        this.error.emit(error);
      }
    });
  }

  private findPoolDetails(): void {
    this.accountPoolService.findPool(this.membershipNumber).subscribe({
      next: (response: PoolResponse) => {
        this.poolInfo = response;
        this.isLoading = false;
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to load pool details');
        this.error.emit(error);
      }
    });
  }

  private handleApiError(error: any, fallback: string): string {
    if (error?.error?.message) {
      return error.error.message;
    }
    if (error?.message) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return fallback;
  }

  acceptInvite(): void {
    if (!this.poolInfo?.ENTITYID) {
      this.errorMessage = 'Pool information not available';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.accountPoolService.processPoolInvite(
      this.poolInfo!.ENTITYID,
      this.membershipNumber,
      'ACCP', // Accept
      this.membershipNumber
    ).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        this.successMessage = 'Invitation accepted successfully!';
        this.hasInvite = false;
        this.inviteAccepted.emit(response);
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to accept invitation');
        this.error.emit(error);
      }
    });
  }

  declineInvite(): void {
    if (!this.poolInfo?.ENTITYID) {
      this.errorMessage = 'Pool information not available';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.accountPoolService.processPoolInvite(
      this.poolInfo!.ENTITYID,
      this.membershipNumber,
      'EXIT', // Decline by exiting
      this.membershipNumber
    ).subscribe({
      next: (response: any) => {
        this.isLoading = false;
        this.successMessage = 'Invitation declined';
        this.hasInvite = false;
        this.inviteDeclined.emit(response);
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to decline invitation');
        this.error.emit(error);
      }
    });
  }
}
