// Styles can be added here if needed

/* Component-specific styles */
:host {
  display: block;
  margin: 1rem 0;
}

form {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: 8px;
  background-color: var(--ion-color-primary);
  color: white;
}

h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--ion-color-dark);
  text-align: center;
  margin-bottom: 1.5rem;
}

h3 {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--ion-color-dark);
  margin-bottom: 1rem;
}

p {
  font-size: 0.875rem;
  color: var(--ion-color-dark);
  line-height: 1.5;
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--ion-color-dark);
}

input, select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--ion-color-medium);
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
  background-color: white;
  color: var(--ion-color-dark);
  
  &:focus {
    border-color: var(--ion-color-primary);
    box-shadow: 0 0 0 2px rgba(var(--ion-color-primary-rgb), 0.25);
    outline: none;
  }
  
  &:disabled {
    background-color: var(--ion-color-light-shade);
    cursor: not-allowed;
  }

  &::placeholder {
    color: var(--ion-color-medium); /* Medium gray for placeholder text */
  }
}

button {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--ion-color-primary);
  color: white;
  font-weight: 600;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  
  &:hover:not(:disabled) {
    background-color: var(--ion-color-primary-shade);
  }
  
  &:active:not(:disabled) {
    transform: translateY(1px);
  }
  
  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
}

.error-message {
  color: var(--ion-color-danger);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1.5rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  table-layout: fixed;
  
  th {
    background-color: #f8f9fa;
    text-align: left;
    padding: 8px;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    font-size: 0.875rem;
    color: #495057;
    
    &:first-child {
      width: 50%;
    }
    
    &:nth-child(2) {
      width: 25%;
      text-align: center;
    }
    
    &:nth-child(3) {
      width: 25%;
      text-align: right;
    }
  }
  
  td {
    padding: 8px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
    word-wrap: break-word;
    font-size: 0.875rem;
    
    &:first-child {
      width: 50%;
    }
    
    &:nth-child(2) {
      width: 25%;
      text-align: center;
    }
    
    &:nth-child(3) {
      width: 25%;
      text-align: right;
      font-weight: 500;
      color: #28a745;
      font-size: 0.8rem;
    }
  }
  
  tbody tr {
    &:hover {
      background-color: #f8f9fa;
    }
    
    &:last-child td {
      border-bottom: none;
    }
  }
}

.member-name-container {
  display: flex;
  flex-direction: column;
  
  .member-name {
    font-weight: 500;
    color: #212529;
    margin-bottom: 0.125rem;
    font-size: 0.875rem;
    line-height: 1.2;
  }
  
  .member-id {
    font-size: 0.7rem;
    color: #6c757d;
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 12px;
    display: inline-block;
    width: fit-content;
    font-family: monospace;
  }
}

/* Override Tailwind classes with our custom styling */
::ng-deep {
  .w-full {
    width: 100%;
  }
  
  .p-2 {
    padding: 0.5rem;
  }
  
  .py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }
  
  .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .mb-4 {
    margin-bottom: 1rem;
  }
  
  .mt-1 {
    margin-top: 0.25rem;
  }
  
  .text-sm {
    font-size: 0.875rem;
  }
  
  .text-xl {
    font-size: 1.25rem;
  }
  
  .font-bold {
    font-weight: 700;
  }
  
  .font-medium {
    font-weight: 500;
  }
  
  .text-center {
    text-align: center;
  }
  
  .rounded-lg {
    border-radius: 0.5rem;
  }
  
  .border {
    border-width: 1px;
  }
}

.member-list {
  margin-top: 2rem;
  
  .member-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid var(--ion-color-light-shade);
    
    &:last-child {
      border-bottom: none;
    }
    
    .member-name {
      font-weight: 500;
    }
    
    .member-info {
      color: var(--ion-color-medium);
      font-size: 0.875rem;
    }
  }
}

.type-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  display: inline-block;
  line-height: 1.2;
  white-space: nowrap;
  
  &.admin {
    background-color: #ff6b35;
    color: white;
  }
  
  &.member {
    background-color: #e9ecef;
    color: #495057;
  }
}

// Responsive design for smaller screens
@media (max-width: 768px) {
  table {
    th, td {
      padding: 6px 4px;
      font-size: 0.7rem;
    }
    
    .member-name-container {
      .member-name {
        font-size: 0.8rem;
      }
      
      .member-id {
        font-size: 0.65rem;
        padding: 1px 4px;
      }
    }
    
    .type-badge {
      font-size: 0.65rem;
      padding: 2px 6px;
    }
  }
}
