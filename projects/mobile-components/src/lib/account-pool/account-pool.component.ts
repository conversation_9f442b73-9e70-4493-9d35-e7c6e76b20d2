import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AccountPoolService, SystemService } from 'lp-client-api';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

interface PoolResponse {
  ENTITYID: number;
  CIS: number;
  MPACC: string;
  ACCALIAS: string;
  POOLSPLIT: number;
  STATUS: string;
  BEGINDATE: string;
  ENDDATE: string | null;
  POOLNAME: string;
  LANGUAGE: string;
  EMAIL: string;
  CONTACTNUMBER: string;
  AUDITUSER: string;
  AUDITDATE: string;
  TOTALUNITS: number;
  members: PoolMember[];
}

interface PoolMember {
  MPACC: string;
  ACCALIAS: string;
  TYPE: string;
  PRIVACY: string;
  MEMBERSTATUS: string;
  BALANCE: number;
  ALLOWACTIVITY: string;
  ALLOWAWARD: string;
  INVITESTATUS: string | null;
  NAME: string;
  AUDITUSER: string;
  AUDITDATE: string;
  ACTIONS: string;
}

interface ApiResponse {
  status: string;
  [key: string]: any;
}

interface ApiError {
  error?: {
    message?: string;
  };
  [key: string]: any;
}

@Component({
  selector: 'lib-account-pool',
  templateUrl: './account-pool.component.html',
  styleUrls: ['./account-pool.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class AccountPoolComponent implements OnInit {
  @Input() membershipNumber!: any;
  @Input() containerClass = 'w-full max-w-md mx-auto p-4 bg-white rounded-lg shadow';
  @Input() titleClass = 'text-xl font-bold mb-4 text-center';
  @Input() formGroupClass = 'mb-4';
  @Input() labelClass = 'block text-sm font-medium text-gray-700 mb-1';
  @Input() inputClass = 'w-full p-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500';
  @Input() buttonClass = 'w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50';
  @Input() errorClass = 'text-sm text-red-600 mt-1';
  @Input() tableClass = 'w-full border-collapse';
  @Input() tableHeaderClass = 'bg-gray-100 text-left p-2 border';
  @Input() tableCellClass = 'p-2 border';

  @Output() poolCreated = new EventEmitter<any>();
  @Output() poolJoined = new EventEmitter<any>();
  @Output() inviteProcessed = new EventEmitter<any>();
  @Output() error = new EventEmitter<any>();

  poolInfo: PoolResponse | null = null;
  poolMembers: PoolMember[] = [];
  isAdmin = false;
  isLoading = false;
  errorMessage = '';
  successMessage = '';
  view: 'find' | 'create' | 'join' | 'details' = 'find';
  
  createPoolForm: FormGroup = {} as FormGroup;
  joinPoolForm: FormGroup = {} as FormGroup;
  inviteMemberForm: FormGroup = {} as FormGroup;

  // Add property for split types
  splitTypes: Array<{code: string, description: string}> = [];

  constructor(
    private fb: FormBuilder,
    private accountPoolService: AccountPoolService,
    private systemService: SystemService
  ) { }

  ngOnInit(): void {
    this.initForms();
    this.loadSplitTypes();
    if (this.membershipNumber) {
      this.findPool();
    }
  }

  private loadSplitTypes(): void {
    this.systemService.getCodeGroup('PSLT').subscribe({
      next: (response: any) => {
        if (response && response.codeItem && Array.isArray(response.codeItem)) {
          this.splitTypes = response.codeItem.map((item: any) => ({
            code: item.code || item.CODE,
            description: item.description || item.DESCRIPTION
          }));
        }
      },
      error: (error: any) => {
        console.error('Failed to load split types:', error);
        // Fallback to hardcoded values if API fails
        this.splitTypes = [
          { code: '1', description: 'Equal Split' },
          { code: '2', description: 'Percentage Split' },
          { code: '3', description: 'Admin Control' }
        ];
      }
    });
  }

  private initForms(): void {
    this.createPoolForm = this.fb.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      countryCode: ['', [Validators.required]],
      telephone: ['', [Validators.required]],
      split: ['', [Validators.required]] // Changed from 0 to empty string
    });

    this.joinPoolForm = this.fb.group({
      poolId: ['', [Validators.required, Validators.pattern(/^\d+$/)]]
    });

    this.inviteMemberForm = this.fb.group({
      membershipNumber: ['', [Validators.required, Validators.minLength(5)]]
    });
  }

  findPool(): void {
    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.accountPoolService.findPool(this.membershipNumber).subscribe({
      next: (response: PoolResponse) => {
        this.poolInfo = response;
        
        if (response && response.members) {
          this.poolMembers = response.members;
          
          // Check if current user is an admin
          const adminMember = this.poolMembers.find(
            member => member.MPACC.trim() === this.membershipNumber && member.TYPE === 'ADMN'
          );
          
          this.isAdmin = !!adminMember;
          this.view = 'details';
        } else {
          this.view = 'find';
        }
        
        this.isLoading = false;
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to find pool information.');
        this.error.emit(error);
        // If error, probably no pool exists, so stay on find view
        this.view = 'find';
      }
    });
  }

  createPool(): void {
    if (this.createPoolForm.invalid) {
      this.markFormGroupTouched(this.createPoolForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const { name, email, countryCode, telephone, split } = this.createPoolForm.value;

    this.accountPoolService.createPool(
      this.membershipNumber,
      'en', // Default language, could be made configurable
      name,
      email,
      countryCode,
      telephone,
      split
    ).subscribe({
      next: (response: ApiResponse) => {
        this.isLoading = false;
        this.successMessage = 'Pool created successfully!';
        this.poolCreated.emit(response);
        this.createPoolForm.reset();
        this.findPool(); // Refresh pool info
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to create pool. Please try again.');
        this.error.emit(error);
      }
    });
  }

  requestToJoinPool(): void {
    if (this.joinPoolForm.invalid) {
      this.markFormGroupTouched(this.joinPoolForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const { poolId } = this.joinPoolForm.value;

    this.accountPoolService.joinPool(
      poolId,
      this.membershipNumber,
      'REQS', // Request to join
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        this.isLoading = false;
        this.successMessage = 'Join request sent successfully!';
        this.poolJoined.emit(response);
        this.joinPoolForm.reset();
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to request pool join. Please try again.');
        this.error.emit(error);
      }
    });
  }

  inviteMember(): void {
    if (this.inviteMemberForm.invalid || !this.poolInfo?.ENTITYID) {
      this.markFormGroupTouched(this.inviteMemberForm);
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    const { membershipNumber } = this.inviteMemberForm.value;

    this.accountPoolService.joinPool(
      this.poolInfo!.ENTITYID,
      membershipNumber,
      'INVT', // Invite
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        this.isLoading = false;
        this.successMessage = 'Invitation sent successfully!';
        this.inviteMemberForm.reset();
        this.findPool(); // Refresh pool info
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to send invitation. Please try again.');
        this.error.emit(error);
      }
    });
  }

  removeMember(membershipNumber: string): void {
    if (!this.isAdmin || !this.poolInfo?.ENTITYID) {
      this.errorMessage = 'You do not have permission to perform this action.';
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.accountPoolService.processPoolInvite(
      this.poolInfo!.ENTITYID,
      membershipNumber,
      'REMV', // Remove member
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        this.isLoading = false;
        this.successMessage = 'Member removed successfully!';
        this.findPool(); // Refresh pool info
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        this.errorMessage = this.handleApiError(error, 'Failed to remove member. Please try again.');
        this.error.emit(error);
      }
    });
  }

  exitPool(): void {
    if (!this.poolInfo?.ENTITYID) {
      this.errorMessage = 'No pool information available.';
      return;
    }

    // Show confirmation dialog
    const confirmed = confirm(
      `Are you sure you want to exit the pool "${this.poolInfo.POOLNAME}"?\n\n` +
      'This action cannot be undone. You will lose access to all pool benefits and ' +
      'will need to be re-invited to join again.'
    );

    if (!confirmed) {
      return;
    }

    this.isLoading = true;
    this.errorMessage = '';
    this.successMessage = '';

    this.accountPoolService.processPoolInvite(
      this.poolInfo!.ENTITYID,
      this.membershipNumber,
      'EXIT', // Exit pool
      this.membershipNumber
    ).subscribe({
      next: (response: ApiResponse) => {
        this.isLoading = false;
        this.successMessage = 'You have successfully exited the pool. You will be redirected to the main pool view.';
        
        // Clear pool data
        this.poolInfo = null;
        this.poolMembers = [];
        this.isAdmin = false;
        
        // Small delay to show success message before changing view
        setTimeout(() => {
          this.view = 'find';
          this.successMessage = '';
        }, 2000);
      },
      error: (error: ApiError) => {
        this.isLoading = false;
        const errorMsg = this.handleApiError(error, 'Failed to exit pool. Please try again.');
        this.errorMessage = errorMsg;
        this.error.emit({
          type: 'exitPoolError',
          message: errorMsg,
          originalError: error
        });
        
        // Log error for debugging
        console.error('Exit pool error:', error);
      }
    });
  }

  changeView(view: 'find' | 'create' | 'join' | 'details'): void {
    this.view = view;
  }

  private markFormGroupTouched(formGroup: FormGroup) {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control) {
        control.markAsTouched();
      }
    });
  }

  hasError(form: FormGroup, controlName: string, errorType: string): boolean {
    const control = form.get(controlName);
    return !!control && control.touched && control.hasError(errorType);
  }

  private handleApiError(error: any, fallback: string): string {
    // Check for specific 404 error indicating member not in pool
    if (error?.status === 404 || (error?.error && error.error.includes('404'))) {
      return 'Member is currently not in a pool';
    }
    
    // Check for HTTP failure response patterns
    if (error?.message && error.message.includes('Http failure response')) {
      return 'Member is currently not in a pool';
    }
    
    if (error?.error?.message) {
      return error.error.message;
    }
    if (error?.message) {
      return error.message;
    }
    if (typeof error === 'string') {
      return error;
    }
    return fallback;
  }

  /**
   * Parses a date string that may contain timezone indicator like [UTC]
   * @param dateString The date string to parse
   * @returns A valid JavaScript Date object or null if parsing fails
   */
  /**
   * Formats status code to human readable text
   * @param status The status code to format
   * @returns Formatted status text
   */
  formatStatus(status: string): string {
    const statusMap: { [key: string]: string } = {
      'STAA': 'Active',
      'INAC': 'Inactive',
      'PEND': 'Pending',
      'SUSP': 'Suspended'
    };
    return statusMap[status] || status;
  }

  parseDate(dateString: string | null | undefined): Date | null {
    if (!dateString) {
      return null;
    }
    
    try {
      // Remove timezone indicator if present
      const cleanDateString = dateString.replace(/\[.*\]$/, '');
      
      // Create a date object from the cleaned string
      const date = new Date(cleanDateString);
      
      // Check if the date is valid
      return isNaN(date.getTime()) ? null : date;
    } catch (error) {
      console.error('Error parsing date:', error);
      return null;
    }
  }
}
