/* Modern Header Design */
.modern-header {
    position: relative;
    padding: 24px 16px 40px;
    background: linear-gradient(135deg, var(--ion-color-base, #0072bc) 0%, var(--ion-color-base-shade, #00569a) 100%);
    overflow: hidden;
    animation: fadeIn 0.6s ease-out;
}

/* Animated decoration circles */
.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 3s ease-in-out infinite;
}

.circle-1 {
    width: 120px;
    height: 120px;
    top: -60px;
    right: -60px;
    animation-delay: 0s;
}

.circle-2 {
    width: 80px;
    height: 80px;
    top: 80px;
    left: -40px;
    animation-delay: 1s;
}

.circle-3 {
    width: 60px;
    height: 60px;
    bottom: 40px;
    right: 20px;
    animation-delay: 2s;
}

/* Header content */
.header-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.time-greeting {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 8px;
    animation: slideDown 0.5s ease-out;
}

.time-greeting ion-icon {
    font-size: 20px;
    vertical-align: middle;
    margin-right: 8px;
}

.user-name {
    font-size: 32px;
    font-weight: 700;
    color: white;
    margin: 0 0 24px 0;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    animation: slideUp 0.6s ease-out;
}

.logo-badge {
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background: white;
    border-radius: 24px;
    padding: 16px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    animation: scaleIn 0.7s cubic-bezier(0.4, 0, 0.2, 1);
    transition: transform 0.3s ease;
}

.logo-badge:active {
    transform: scale(0.95);
}

.logo-badge img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* Legacy styles for compatibility */
.center {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.points {
    --background: var(--ion-color-primary);
    width: 100%;
    margin-left: 20px;
    height: 40px;
    box-shadow: 0px 0px 25px rgba(0, 0, 0, 0.6);
}

.logo {
    height: 80px;
    margin-right: 15px;
}

.welcome {
    width: 100%;
    text-align: center;
    margin-top: -10px;
}

.welcome h5 {
    text-align: center;
    margin: 0;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
}

.welcome .front-text {
    display: block;
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    color: white;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Responsive adjustments */
@media (max-width: 320px) {
    .user-name {
        font-size: 26px;
    }
    
    .logo-badge {
        width: 70px;
        height: 70px;
        padding: 14px;
    }
}