<!-- Modern Header with Animation -->
<div class="modern-header" *ngIf="type == 'welcome'">
  <!-- Animated decoration circles -->
  <div class="decoration-circle circle-1"></div>
  <div class="decoration-circle circle-2"></div>
  <div class="decoration-circle circle-3"></div>
  
  <div class="header-content">
    <p class="time-greeting">
      <ion-icon [name]="getTimeIcon()"></ion-icon>
      {{ getTimeGreeting() }}
    </p>
    <h1 class="user-name">{{ names }}</h1>
    <div class="logo-badge">
      <img [src]="src" alt="Logo" />
    </div>
  </div>
</div>

<!-- Legacy layout for other types -->
<ion-grid *ngIf="type != 'welcome'">
  <ion-row class="center">
    <div>
      <ion-button
        (click)="showText()"
        *ngIf="type == 'balance' || type == 'membership'"
        expand="block"
        class="points"
        >{{ useText }}</ion-button
      >
      <a
        *ngIf="type == 'phone'"
        style="text-decoration: none"
        href="{{ 'tel:' + phone }}"
      >
        <ion-button (click)="showText()" expand="block" class="points">
          <ion-icon size="25px" class="icon-call" name="call"></ion-icon>

          {{ phone }}</ion-button
        >
      </a>
    </div>

    <div class="logo-col">
      <img class="logo" [src]="src" alt="" />
    </div>
  </ion-row>
</ion-grid>
