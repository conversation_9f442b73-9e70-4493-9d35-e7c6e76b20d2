// Enhanced Home Screen Design
// More impactful visual changes while keeping the structure

// Design Variables
:root {
  // Enhanced Color Palette
  --ion-color-base: #0072bc;
  --ion-color-base-darker: #00487a;
  --ion-color-base-lighter: #0084db;
  
  --ion-color-primary: #FF6B35;
  --ion-color-primary-darker: #E55525;
  --ion-color-primary-lighter: #FF8555;
  
  // Action Button Colors - Different colors for each action
  --color-profile: #FFB74D;  // Warm orange
  --color-card: #4FC3F7;     // Light blue
  --color-transactions: #81C784;  // Green
  --color-stores: #BA68C8;   // Purple
  
  // Enhanced shadows
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
  --shadow-colored: 0 8px 32px rgba(255, 107, 53, 0.3);
}

// ENHANCED HEADER SECTION
.head-logo-enhanced {
  background: linear-gradient(135deg, var(--ion-color-base) 0%, var(--ion-color-base-darker) 100%);
  padding: 24px 16px 32px;
  border-radius: 0 0 24px 24px;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
  
  // Decorative background pattern
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -30%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.05) 0%, transparent 70%);
    transform: rotate(-45deg);
  }
  
  // Logo section with glow effect
  .logo-section {
    position: relative;
    text-align: right;
    margin-bottom: 16px;
    
    img {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      background: white;
      padding: 8px;
      box-shadow: 0 4px 16px rgba(255,255,255,0.2);
    }
  }
  
  // Welcome text with better styling
  .welcome-section {
    position: relative;
    z-index: 1;
    
    .welcome-label {
      font-size: 14px;
      color: rgba(255, 255, 255, 0.7);
      font-weight: 400;
      margin-bottom: 4px;
      letter-spacing: 0.5px;
    }
    
    .user-name {
      font-size: 28px;
      font-weight: 700;
      color: white;
      line-height: 1.2;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

// ENHANCED BALANCE CARDS
.balance-cards-container {
  margin-top: -16px;
  padding: 0 16px;
  display: flex;
  gap: 12px;
  
  .balance-card-enhanced {
    flex: 1;
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    
    // Gradient overlay for Rand Value
    &.rand-value {
      background: linear-gradient(135deg, var(--ion-color-primary) 0%, var(--ion-color-primary-darker) 100%);
      color: white;
      
      .card-icon {
        position: absolute;
        top: -20px;
        right: -20px;
        font-size: 80px;
        opacity: 0.1;
        color: white;
      }
    }
    
    // Points card with border
    &.points {
      border: 2px solid var(--ion-color-primary);
      
      .value {
        color: var(--ion-color-primary);
      }
      
      .card-icon {
        position: absolute;
        bottom: -20px;
        right: -20px;
        font-size: 80px;
        opacity: 0.05;
        color: var(--ion-color-primary);
      }
    }
    
    .label {
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      opacity: 0.8;
      margin-bottom: 8px;
    }
    
    .value {
      font-size: 26px;
      font-weight: 700;
      line-height: 1;
      position: relative;
      z-index: 1;
    }
  }
}

// ENHANCED ACTION BUTTONS
.action-buttons-grid {
  padding: 32px 16px;
  
  .action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  
  .action-item {
    text-align: center;
    position: relative;
    
    .action-button-enhanced {
      width: 100%;
      aspect-ratio: 1;
      border-radius: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 12px;
      padding: 20px;
      padding-top: 30px; // Extra space for the overlapping icon
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: visible; // Allow icon to overflow at the top
      color: white;
      text-decoration: none;
      
      // Different gradients for each button
      &.profile {
        background: linear-gradient(135deg, var(--color-profile) 0%, #FF9800 100%);
        box-shadow: 0 8px 24px rgba(255, 183, 77, 0.4);
      }
      
      &.card {
        background: linear-gradient(135deg, var(--color-card) 0%, #29B6F6 100%);
        box-shadow: 0 8px 24px rgba(79, 195, 247, 0.4);
      }
      
      &.transactions {
        background: linear-gradient(135deg, var(--color-transactions) 0%, #66BB6A 100%);
        box-shadow: 0 8px 24px rgba(129, 199, 132, 0.4);
      }
      
      &.stores {
        background: linear-gradient(135deg, var(--color-stores) 0%, #AB47BC 100%);
        box-shadow: 0 8px 24px rgba(186, 104, 200, 0.4);
      }
      
      // Hover/Active effects
      &:active {
        transform: scale(0.95);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }
      
      // Icon container - positioned at top center, slightly overlapping
      .icon-wrapper {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        position: absolute;
        top: -20px; // Stick out at the top
        left: 50%;
        transform: translateX(-50%); // Center horizontally
        z-index: 2;
        border: 3px solid rgba(255, 255, 255, 0.3);
      }
      
      ion-icon {
        font-size: 28px;
        color: white;
      }
      
      // Label - positioned in center of card
      .button-label {
        font-size: 14px;
        font-weight: 600;
        color: white;
        margin: 0;
        margin-top: 20px; // Space from the top to center the text
        text-align: center;
      }
      
      // Decorative corner accent
      &::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);
      }
    }
  }
}

// Alternative design with floating action buttons
.action-buttons-floating {
  padding: 24px 16px;
  
  .floating-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .floating-action {
    background: white;
    border-radius: 20px;
    padding: 24px;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    
    &:active {
      transform: translateY(2px);
      box-shadow: var(--shadow-sm);
    }
    
    // Colored accent bar
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
    }
    
    &.profile::before { background: var(--color-profile); }
    &.card::before { background: var(--color-card); }
    &.transactions::before { background: var(--color-transactions); }
    &.stores::before { background: var(--color-stores); }
    
    .action-content {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .icon-bubble {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.profile { background: rgba(255, 183, 77, 0.1); }
        &.card { background: rgba(79, 195, 247, 0.1); }
        &.transactions { background: rgba(129, 199, 132, 0.1); }
        &.stores { background: rgba(186, 104, 200, 0.1); }
        
        ion-icon {
          font-size: 28px;
          
          &.profile { color: var(--color-profile); }
          &.card { color: var(--color-card); }
          &.transactions { color: var(--color-transactions); }
          &.stores { color: var(--color-stores); }
        }
      }
      
      .action-text {
        flex: 1;
        
        .action-title {
          font-size: 16px;
          font-weight: 600;
          color: #212121;
          margin: 0 0 4px 0;
        }
        
        .action-subtitle {
          font-size: 12px;
          color: #757575;
          margin: 0;
        }
      }
      
      .arrow-icon {
        color: #BDBDBD;
        font-size: 20px;
      }
    }
  }
}