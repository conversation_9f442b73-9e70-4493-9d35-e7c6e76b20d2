<div class="app-background">
  <!-- Modern Header Component -->
  <lib-head-logo
    [names]="profile.givenNames + ' ' + profile.surname"
    [membership]="profile.newMembershipNumber"
    type="welcome"
    [balance]="profile.currentBalance"
    [src]="lssConfig.pages.landing.loggedinIcon"
  />
  
  <!-- Single Balance Card -->
  <div class="balance-section">
    <div class="balance-card">
      <div class="balance-item rand-value">
        <div class="balance-header">
          <p class="balance-label">RAND VALUE</p>
          <div class="balance-icon">
            <ion-icon name="cash-outline"></ion-icon>
          </div>
        </div>
        <p class="balance-value">R {{ formatCurrency(profile?.availRands) }}</p>
      </div>
      
      <div class="balance-divider"></div>
      
      <div class="balance-item points">
        <div class="balance-header">
          <p class="balance-label">POINTS</p>
          <div class="balance-icon">
            <ion-icon name="star-outline"></ion-icon>
          </div>
        </div>
        <p class="balance-value points-value">{{ formatNumber(profile?.currentBalance) }}</p>
      </div>
    </div>
  </div>

  <!-- Modern Action Buttons -->
  <div class="modern-actions">
    <h2 class="actions-title">Quick Actions</h2>
    <div class="modern-grid">
      <!-- Profile -->
      <a (click)="navigateWithAnimation('/app/account', 'profile')" 
         class="modern-action-card profile"
         [class.animating-diagonal]="animatingCard === 'profile'">
        <div class="action-icon">
          <ion-icon name="person-outline"></ion-icon>
        </div>
        <p class="action-label">Profile</p>
      </a>
      
      <!-- Card -->
      <a (click)="navigateWithAnimation('/app/virtualcard', 'card')" 
         class="modern-action-card card"
         [class.animating-diagonal]="animatingCard === 'card'">
        <div class="action-icon">
          <ion-icon name="card-outline"></ion-icon>
        </div>
        <p class="action-label">Card</p>
      </a>
      
      <!-- Transactions -->
      <a (click)="navigateWithAnimation('/app/transactions', 'transactions')" 
         class="modern-action-card transactions"
         [class.animating-diagonal]="animatingCard === 'transactions'">
        <div class="action-icon">
          <ion-icon name="receipt-outline"></ion-icon>
        </div>
        <p class="action-label">Transactions</p>
      </a>
      
      <!-- Stores -->
      <a (click)="navigateWithAnimation('/app/stores', 'stores')" 
         class="modern-action-card stores"
         [class.animating-diagonal]="animatingCard === 'stores'">
        <div class="action-icon">
          <ion-icon name="storefront-outline"></ion-icon>
        </div>
        <p class="action-label">Stores</p>
      </a>
    </div>
  </div>
</div>
