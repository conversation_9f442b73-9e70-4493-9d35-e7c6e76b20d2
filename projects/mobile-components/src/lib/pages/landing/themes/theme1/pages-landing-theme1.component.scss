/* Modern App Background */
.app-background {
  --background: #f5f7fa;
  position: relative;
}

/* Single Balance Card */
.balance-section {
  margin-top: -30px;
  padding: 0 24px 16px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;
}

.balance-card {
  background: white;
  border-radius: 20px;
  padding: 0;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  overflow: hidden;
  align-items: stretch; // Ensure both sections stretch to full height
}

.balance-item {
  flex: 1;
  padding: 20px;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 90px;
  
  &.rand-value {
    background: var(--ion-color-primary, #FF6B35);
    color: white;
    
    .balance-icon {
      background: rgba(255, 255, 255, 0.2);
      color: white;
    }
  }
  
  &.points {
    background: white;
    
    .balance-label {
      color: var(--ion-color-primary, #FF6B35);
    }
    
    .balance-icon {
      background: rgba(255, 107, 53, 0.1);
      color: var(--ion-color-primary, #FF6B35);
    }
  }
}

.balance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.balance-label {
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.5px;
  opacity: 0.8;
  margin: 0;
}

.balance-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  ion-icon {
    font-size: 20px;
  }
}

.balance-value {
  font-size: 20px;
  font-weight: 700;
  line-height: 1;
  margin: 0;
  
  &.points-value {
    color: var(--ion-color-primary, #FF6B35);
  }
}

.balance-divider {
  width: 1px;
  background: rgba(0, 0, 0, 0.1);
  margin: 16px 0;
  align-self: stretch; // Stretch to full height
}

/* Modern Action Grid */
.modern-actions {
  padding: 16px 24px 32px;
}

.actions-title {
  font-size: 20px;
  font-weight: 600;
  color: #212121;
  margin-bottom: 20px;
}

.modern-grid {
  display: grid;
  grid-template-columns: repeat(2, 2fr);
  gap: 16px;
  grid-auto-rows: 100px; // Fixed height for consistent alignment
  align-items: stretch; // Ensure all cards stretch to fill grid cells
}

.modern-action-card {
  background: white;
  border-radius: 20px;
  padding: 20px;
  height: 100px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  cursor: pointer;
  gap: 4px;
}

.modern-action-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.12);
}

/* Action card gradients */
.modern-action-card.profile {
  background: linear-gradient(135deg, #FFE0B2 0%, #FFCC80 100%);
}

.modern-action-card.card {
  background: linear-gradient(135deg, #E1F5FE 0%, #B3E5FC 100%);
}

.modern-action-card.transactions {
  background: linear-gradient(135deg, #E8F5E9 0%, #C8E6C9 100%);
}

.modern-action-card.stores {
  background: linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%);
}

/* Action icons - centered within cards */
.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  position: relative;
  border: 2px solid white;
  margin-bottom: 0;
  flex-shrink: 0;
}

.modern-action-card.profile .action-icon { background: #FF6F00; }
.modern-action-card.card .action-icon { background: #0288D1; }
.modern-action-card.transactions .action-icon { background: #388E3C; }
.modern-action-card.stores .action-icon { background: #7B1FA2; }

.action-icon ion-icon {
  font-size: 24px;
  color: white;
}

.action-label {
  font-size: 14px;
  font-weight: 600;
  color: #212121;
  margin: 0;
  text-align: center;
  line-height: 1.2;
}

/* Diagonal animation for card exit */
.modern-action-card.animating-diagonal {
  animation: diagonalExit 0.6s ease-out forwards;
  z-index: 100;
}

.modern-action-card.animating-diagonal .action-icon {
  animation: iconSpin 0.6s ease-out;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes diagonalExit {
  0% {
    transform: scale(1) translate(0, 0);
    opacity: 1;
  }
  100% {
    transform: scale(1.2) translate(150px, -150px) rotate(15deg);
    opacity: 0;
  }
}

@keyframes iconSpin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments */
@media (max-width: 375px) {
  .balance-section {
    padding: 0 16px 12px;
  }
  
  .balance-item {
    padding: 16px;
  }
  
  .balance-value {
    font-size: 18px;
  }
  
  .balance-label {
    font-size: 10px;
  }
  
  .balance-icon {
    width: 32px;
    height: 32px;
  }
  
  .balance-icon ion-icon {
    font-size: 18px;
  }
  
  .modern-actions {
    padding: 12px 16px 24px;
  }
  
  .actions-title {
    font-size: 18px;
    margin-bottom: 16px;
  }
  
  .modern-action-card {
    padding: 16px;
    gap: 2px;
  }

  .action-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 0;
  }

  .action-icon ion-icon {
    font-size: 20px;
  }

  .action-label {
    font-size: 12px;
  }
}

.landing_old {
  // background-image: url("~/src/assets/images/landing.jfif");
  background-size: cover;
  background-position: center;
  --background: transparent;
  .header {
    text-align: center;

    .logo {
      position: relative;
      padding-bottom: 30px;
    }

    img {
      margin: auto;
      display: block;
    }
  }

  ion-card {
    box-shadow: none;
    border-radius: 20px;

    h1 {
      font-weight: bold;
      font-size: 20px;
      text-align: center;
      margin-bottom: 5px;
    }

    p {
      text-align: center;
      font-size: 16px;
      margin-bottom: 45px;
    }

    .line-text {
      padding: 2px;
      margin-top: 20px;
      position: relative;

      .line {
        height: 1px;
        background: #858585;
        display: block;
      }

      .text {
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
        background: #fff;
        padding: 0 15px;
        font-size: 10px;
        font-weight: 500;
      }

      //@media (prefers-color-scheme: dark) {
      //    .text {
      //        background: #1e1e1e;
      //    }
      //}
    }
  }
}

ion-toolbar {
  display: none;
}
.landing2 {
  --background: var(--ion-color-base);
  padding: 40px;
}
.landing {
  --background: var(--ion-color-base);
  padding: 20px;
  position: relative;
  height: 100vh;

  .logo {
    position: absolute;
    width: 100vw;
    top: 4%;
    -ms-transform: translateY(-4%);
    transform: translateY(-4%);
    display: flex;
    justify-content: center;

    img {
      width: 50vw;
      max-width: 300px;
    }
  }

  .content {
    position: absolute;
    width: 100vw;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;

    ion-button {
      width: 80vw;
    }
  }
  .content-title {
    text-align: center;
  }

  ion-button {
    --background: var(--ion-color-primary);
    --color: var(--ion-color-primaryContrast);
  }
  .login-title {
    color: var(--ion-color-baseContrast);
    margin-bottom: 6px;
  }

  .login-subtitle {
    color: var(--ion-color-baseContrast);
    margin-top: 0;
    margin-bottom: 14px;
    font-weight: 300;
  }

  .socials {
    position: absolute;
    width: 100vw;
    bottom: 3%;
    -ms-transform: translateY(3%);
    transform: translateY(3%);
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    ion-button {
      --background: transparent;
      --box-shadow: none;
    }

    ion-icon {
      color: #fff;
      font-size: 2.4em;
    }
  }
}
