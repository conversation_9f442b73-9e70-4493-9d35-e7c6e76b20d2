import { Component, Input } from '@angular/core';
import { Router } from '@angular/router';
import { LssConfig } from 'lp-client-api';

@Component({
  selector: 'pages-landing-theme1',
  templateUrl: './pages-landing-theme1.component.html',
  styleUrls: [
    './pages-landing-theme1.component.scss',
    '../../../../../styles.scss',
  ],
})
export class PagesLandingTheme1Component {
  @Input() public profile: any;
  animatingCard: string | null = null;
  
  constructor(protected readonly router: Router, public lssConfig: LssConfig) {

  }

  // Format currency with thousand separators
  formatCurrency(value: number | undefined | null): string {
    if (!value && value !== 0) return '0.00';
    
    // Format with 2 decimal places and thousand separators
    return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Format number with thousand separators
  formatNumber(value: number | undefined | null): string {
    if (!value && value !== 0) return '0';
    
    // Format with thousand separators
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Navigate with animation
  navigateWithAnimation(route: string, cardType: string) {
    this.animatingCard = cardType;
    
    // Wait for animation to complete before navigating
    setTimeout(() => {
      this.router.navigate([route]);
      this.animatingCard = null;
    }, 600); // Match animation duration
  }
}
