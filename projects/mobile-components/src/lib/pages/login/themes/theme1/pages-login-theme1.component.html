<div [class]="lssConfig.pages.login.class">
  <div [className]="lssConfig.pages.login.icon.class">
    <img
      [className]="lssConfig.pages.login.icon.class"
      [src]="lssConfig.pages.login.icon.src"
      alt=""
    />
  </div>

  <div class="content">
    <div class="content-title">
      <div [className]="lssConfig.pages.login.title.class">
        {{ lssConfig.pages.login.title.text }}
      </div>
      <br />
      <div [className]="lssConfig.pages.login.subtitle.class">
        {{ lssConfig.pages.login.subtitle.text }}
      </div>
    </div>

    <div [className]="lssConfig.pages.login.auth_buttons.class">
      <button
        [className]="lssConfig.pages.login.auth_buttons.login.class"
        (click)="login()"
      >
        {{ lssConfig.pages.login.auth_buttons.login.text }}
      </button>
      <button
        [className]="lssConfig.pages.login.auth_buttons.signup.class"
        routerLink="/public/validate"
      >
        {{ lssConfig.pages.login.auth_buttons.signup.text }}
      </button>
      <button
        [className]="lssConfig.pages.login.auth_buttons.password.class"
        (click)="lostPassword()"
      >
        {{ lssConfig.pages.login.auth_buttons.password.text }}
      </button>
    </div>
  </div>
  <div [className]="lssConfig.pages.login.social_buttons.class">
    <button [className]="lssConfig.pages.login.social_buttons.facebook.class">
      <a [href]="lssConfig.socials.facebook" target="_blank"
        ><ion-icon
          [name]="lssConfig.pages.login.social_buttons.facebook.icon"
          slot="icon-only"
          [size]="lssConfig.pages.login.social_buttons.facebook.icon_size"
        ></ion-icon
      ></a>
    </button>
    <button [className]="lssConfig.pages.login.social_buttons.twitter.class">
      <a [href]="lssConfig.socials.twitter" target="_blank"
        ><ion-icon
          [name]="lssConfig.pages.login.social_buttons.twitter.icon"
          slot="icon-only"
          [size]="lssConfig.pages.login.social_buttons.twitter.icon_size"
        ></ion-icon
      ></a>
    </button>
    <button [class]="lssConfig.pages.login.social_buttons.linkedin.class">
      <a [href]="lssConfig.socials.linkedin" target="_blank"
        ><ion-icon
          [name]="lssConfig.pages.login.social_buttons.linkedin.icon"
          slot="icon-only"
          [size]="lssConfig.pages.login.social_buttons.linkedin.icon_size"
        ></ion-icon
      ></a>
    </button>
    <button [class]="lssConfig.pages.login.social_buttons.youtube?.class" *ngIf="lssConfig.pages.login.social_buttons.youtube">
      <a [href]="lssConfig.socials.youtube" target="_blank"
        ><ion-icon
          [name]="lssConfig.pages.login.social_buttons.youtube.icon"
          slot="icon-only"
          [size]="lssConfig.pages.login.social_buttons.youtube.icon_size"
        ></ion-icon
      ></a>
    </button>
    <button [class]="lssConfig.pages.login.social_buttons.instagram.class">
      <a [href]="lssConfig.socials.instagram" target="_blank"
        ><ion-icon
          [name]="lssConfig.pages.login.social_buttons.instagram.icon"
          slot="icon-only"
          [size]="lssConfig.pages.login.social_buttons.instagram.icon_size"
        ></ion-icon
      ></a>
    </button>
    <button [class]="lssConfig.pages.login.social_buttons.pinterest.class">
      <a [href]="lssConfig.socials.pinterest" target="_blank"
        ><ion-icon
          [name]="lssConfig.pages.login.social_buttons.pinterest.icon"
          slot="icon-only"
          [size]="lssConfig.pages.login.social_buttons.pinterest.icon_size"
        ></ion-icon
      ></a>
    </button>
  </div>

  <div></div>
</div>
